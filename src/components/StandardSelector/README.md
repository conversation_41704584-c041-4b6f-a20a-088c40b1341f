# StandardSelector 标准选择器组件

基于城市标准体系的通用选择器组件，用于在左右布局中选择标准体系，支持搜索、分页和选择事件。

## 功能特性

- 📋 **简化显示**：只显示名称（分类）和年份两列
- 🔍 **搜索功能**：支持按名称和年份搜索
- 📄 **分页支持**：支持分页显示和自定义每页数量
- 🎯 **选择事件**：点击行触发选择事件，返回选中标准的完整信息
- 🎨 **样式美化**：现代化的表格样式和交互效果
- 🔧 **高度可配置**：支持多种配置选项
- 📱 **响应式设计**：适配不同屏幕尺寸

## 基本用法

```vue
<template>
  <StandardSelector
    @select="handleStandardSelect"
    @change="handleStandardChange"
  />
</template>

<script setup>
import { StandardSelector } from '@/components/StandardSelector'

const handleStandardSelect = (standard) => {
  console.log('选中标准:', standard)
}

const handleStandardChange = (standard) => {
  console.log('选择变化:', standard)
}
</script>
```

## 属性配置

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `showSearch` | `boolean` | `true` | 是否显示搜索区域 |
| `showPagination` | `boolean` | `true` | 是否显示分页 |
| `showStatus` | `boolean` | `false` | 是否显示状态列 |
| `tableHeight` | `string \| number` | `400` | 表格高度 |
| `pageSize` | `number` | `10` | 每页显示数量 |
| `enabledOnly` | `boolean` | `false` | 是否只显示启用的标准 |

## 事件

| 事件名 | 参数 | 说明 |
|--------|------|------|
| `select` | `(standard: CitystandardVO)` | 选择标准时触发 |
| `change` | `(standard: CitystandardVO \| null)` | 选择变化时触发（包括取消选择） |

## 方法

| 方法名 | 参数 | 返回值 | 说明 |
|--------|------|--------|------|
| `getSelectedStandard` | - | `CitystandardVO \| null` | 获取当前选中的标准 |
| `clearSelection` | - | `void` | 清除选择 |
| `refresh` | - | `void` | 刷新列表 |
| `selectById` | `(id: number)` | `void` | 根据ID选中标准 |
| `getList` | - | `Promise<void>` | 获取列表数据 |

## 使用场景

### 1. 左右布局选择器

```vue
<template>
  <div class="layout-container">
    <!-- 左侧：标准选择器 -->
    <div class="left-panel">
      <StandardSelector
        ref="standardSelectorRef"
        :show-search="true"
        :table-height="500"
        @select="handleStandardSelect"
      />
    </div>
    
    <!-- 右侧：相关数据 -->
    <div class="right-panel">
      <div v-if="selectedStandard">
        <h3>{{ selectedStandard.name }} - 相关数据</h3>
        <!-- 这里显示与选中标准相关的数据 -->
      </div>
    </div>
  </div>
</template>

<script setup>
import { StandardSelector } from '@/components/StandardSelector'

const standardSelectorRef = ref()
const selectedStandard = ref(null)

const handleStandardSelect = (standard) => {
  selectedStandard.value = standard
  // 加载相关数据
  loadRelatedData(standard.id)
}

const loadRelatedData = (standardId) => {
  // 根据标准ID加载相关数据
}
</script>
```

### 2. 紧凑模式

```vue
<template>
  <StandardSelector
    :show-search="false"
    :show-pagination="false"
    :table-height="300"
    :page-size="50"
    class="compact"
  />
</template>
```

### 3. 只显示启用的标准

```vue
<template>
  <StandardSelector
    :enabled-only="true"
    :show-status="true"
  />
</template>
```

## 样式定制

组件支持通过 CSS 变量进行样式定制：

```scss
.standard-selector {
  // 自定义搜索区域背景色
  .search-section {
    background: #f0f2f5;
  }
  
  // 自定义表格行悬停效果
  .selector-table :deep(.el-table__row:hover) {
    background-color: #e6f7ff;
  }
  
  // 自定义选中行样式
  .selector-table :deep(.el-table__row.current-row) {
    background-color: #bae7ff;
  }
}
```

## 数据格式

选中的标准数据格式：

```typescript
interface CitystandardVO {
  id?: number
  name?: string
  publishYear?: number
  category?: string
  enabled?: number
  createTime?: Date
  updateTime?: Date
}
```

## 注意事项

1. 组件依赖 `@/api/urban/citystandard` API，确保相关接口可用
2. 使用前需要确保已正确导入组件
3. 表格高度建议根据实际布局需求进行调整
4. 在移动端使用时，建议启用响应式布局
