<template>
  <div class="map-container relative">
    <!-- 使用说明弹窗 -->
    <el-dialog v-model="helpDialogVisible" title="MapDraw 组件使用说明" width="80%" :before-close="() => helpDialogVisible = false">
      <div class="help-content">
        <h3>🗺️ MapDraw 通用地图绘制组件演示</h3>
        
        <div class="help-section">
          <h4>📖 功能概述</h4>
          <p>本页面演示了 MapDraw 地图绘制组件的完整功能，包括：</p>
          <ul>
            <li>支持点、线、面三种几何图形绘制</li>
            <li>支持多图形绘制或单图形模式</li>
            <li>支持WKT格式数据导入导出</li>
            <li>支持参考数据展示</li>
            <li>内嵌组件和弹窗两种使用方式</li>
            <li>完整的编辑、撤销、重做功能</li>
          </ul>
        </div>

        <div class="help-section">
          <h4>🚀 快速开始</h4>
          <ol>
            <li>点击右上角 <strong>"开始绘制"</strong> 按钮进入绘制模式</li>
            <li>在 <strong>MapDrawCore 组件</strong> 区域使用工具栏绘制图形</li>
            <li>尝试 <strong>MapDrawDialog 弹窗</strong> 功能</li>
            <li>使用控制选项调整组件行为</li>
            <li>查看实时生成的WKT数据</li>
          </ol>
        </div>

        <div class="help-section">
          <h4>🔧 组件使用方法</h4>
          <el-tabs>
            <el-tab-pane label="MapDrawCore" name="core">
              <pre><code class="language-vue">&lt;template&gt;
  &lt;MapDrawCore
    v-model="wktData"
    :reference-data="referenceWKTData"
    :show-toolbar="true"
    :allowed-modes="['Point', 'LineString', 'Polygon']"
    :allow-multiple="true"
    :center="[116.404, 39.915]"
    :zoom="10"
    :projection="'EPSG:4326'"
    :readonly="false"
    height="400px"
    width="100%"
    @draw-start="handleDrawStart"
    @draw-end="handleDrawEnd"
    @feature-select="handleFeatureSelect"
    @feature-modify="handleFeatureModify"
    @clear="handleDrawClear"
  /&gt;
&lt;/template&gt;

&lt;script setup&gt;
import { ref } from 'vue'
import MapDrawCore from '@/components/MapDraw/src/MapDrawCore.vue'

const wktData = ref('')
const referenceWKTData = ref([])

const handleDrawEnd = (event) => {
  console.log('绘制完成:', event.wkt)
}
&lt;/script&gt;</code></pre>
            </el-tab-pane>
            
            <el-tab-pane label="MapDrawDialog" name="dialog">
              <pre><code class="language-vue">&lt;template&gt;
  &lt;el-button @click="dialogVisible = true"&gt;打开地图绘制&lt;/el-button&gt;
  
  &lt;MapDrawDialog
    v-model:visible="dialogVisible"
    v-model:model-value="wktData"
    title="地图绘制"
    width="90%"
    height="80%"
    :reference-data="referenceWKTData"
    :allow-multiple="true"
    @confirm="handleConfirm"
  /&gt;
&lt;/template&gt;

&lt;script setup&gt;
import { ref } from 'vue'
import MapDrawDialog from '@/components/MapDraw/src/MapDrawDialog.vue'

const dialogVisible = ref(false)
const wktData = ref('')

const handleConfirm = (wktData) => {
  console.log('确认数据:', wktData)
}
&lt;/script&gt;</code></pre>
            </el-tab-pane>
            
            <el-tab-pane label="FormCreate集成" name="formcreate">
              <pre><code class="language-vue">&lt;template&gt;
  &lt;FormCreate v-model="formData" :rule="formRule" /&gt;
&lt;/template&gt;

&lt;script setup&gt;
import { ref } from 'vue'
import { useMapDrawRule } from '@/components/FormCreate/src/config'

const formData = ref({})
const mapDrawRule = useMapDrawRule()

const formRule = [
  {
    type: 'input',
    field: 'name',
    title: '地块名称'
  },
  {
    type: 'MapDraw',
    field: 'geometry',
    title: '地块范围',
    props: {
      allowMultiple: false,
      allowedModes: ['Polygon'],
      center: [116.404, 39.915],
      zoom: 10
    }
  }
]
&lt;/script&gt;</code></pre>
            </el-tab-pane>
          </el-tabs>
        </div>

        <div class="help-section">
          <h4>⚙️ 主要属性说明</h4>
          <el-table :data="propsData" style="width: 100%" size="small">
            <el-table-column prop="prop" label="属性名" width="150" />
            <el-table-column prop="type" label="类型" width="120" />
            <el-table-column prop="default" label="默认值" width="120" />
            <el-table-column prop="description" label="说明" />
          </el-table>
        </div>

        <div class="help-section">
          <h4>🎯 事件说明</h4>
          <el-table :data="eventsData" style="width: 100%" size="small">
            <el-table-column prop="event" label="事件名" width="150" />
            <el-table-column prop="params" label="参数" width="200" />
            <el-table-column prop="description" label="说明" />
          </el-table>
        </div>

        <div class="help-section">
          <h4>💡 使用技巧</h4>
          <ul>
            <li><strong>多图形模式：</strong> 启用后可绘制多个图形，禁用后只能绘制一个</li>
            <li><strong>参考数据：</strong> 可预先加载背景图形作为参考</li>
            <li><strong>WKT数据：</strong> 标准地理数据格式，可与GIS系统对接</li>
            <li><strong>坐标系统：</strong> 支持EPSG:4326(经纬度)和EPSG:3857(墨卡托)等</li>
            <li><strong>编辑功能：</strong> 点击已绘制图形可进行节点编辑</li>
            <li><strong>撤销重做：</strong> 支持操作历史管理</li>
          </ul>
        </div>
      </div>
      
      <template #footer>
        <el-button type="primary" @click="helpDialogVisible = false">我知道了</el-button>
      </template>
    </el-dialog>

    <!-- 帮助按钮 -->
    <div class="help-button">
      <el-button type="info" circle @click="helpDialogVisible = true" title="查看使用说明">
        <el-icon><QuestionFilled /></el-icon>
      </el-button>
    </div>

    <ol-map :loadTilesWhileAnimating="true" :loadTilesWhileInteracting="true" style="height: calc(100vh - 50px)">
      <ol-view ref="view" :center="center" :projection="projection" :zoom="zoom" />

      <ol-tile-layer>
        <ol-source-tianditu
          :hidpi="true"
          layerType="img"
          projection="EPSG:4326"
          tk="4989e906aa138e5bb1b49a3eb83a6128"
        />
      </ol-tile-layer>

      <ol-tile-layer>
        <ol-source-tianditu
          :hidpi="true"
          :isLabel="true"
          layerType="img"
          projection="EPSG:4326"
          tk="4989e906aa138e5bb1b49a3eb83a6128"
        />
      </ol-tile-layer>
      <ol-vector-layer ref="vectorLayer">
        <ol-source-vector
          :format="GeoJSON"
          :projection="projection"
          :strategy="bbox"
          :url="urlFunction"
        />
        <ol-style :overrideStyleFunction="styleFunction" />
      </ol-vector-layer>

      <ol-interaction-select
        :filter="selectFilter"
        @select="handleSelect"
      >
        <ol-style>
          <ol-style-stroke color="green" :width="10"/>
          <ol-style-fill color="rgba(255,255,255,0.5)"/>
        </ol-style>
      </ol-interaction-select>

      <ol-scaleline-control />
      <ol-mouseposition-control
        :projection="'EPSG:4326'"
        :coordinateFormat="coordinateFormat"
        className="custom-mouse-position"
      />
    </ol-map>

    <!-- 地图绘制工具栏 -->
    <div class="map-draw-toolbar">
      <div class="toolbar-header">
        <h3>地图绘制演示</h3>
        <el-button @click="toggleDrawMode" :type="isDrawMode ? 'danger' : 'primary'" size="small">
          {{ isDrawMode ? '退出绘制' : '开始绘制' }}
        </el-button>
      </div>
      
      <!-- 绘制组件区域 -->
      <div v-if="isDrawMode" class="draw-component-area">
        <div class="component-demo-section">
          <h4>MapDrawCore 组件演示</h4>
          <div class="draw-container">
            <MapDrawCore
              v-model="drawWKTData"
              :reference-data="referenceWKTData"
              :show-toolbar="true"
              :allowed-modes="['Point', 'LineString', 'Polygon']"
              :allow-multiple="allowMultipleShapes"
              :center="center"
              :zoom="zoom"
              :projection="projection"
              :tianditu-token="tiandituToken"
              :readonly="false"
              height="400px"
              width="100%"
              @draw-start="handleDrawStart"
              @draw-end="handleDrawEnd"
              @feature-select="handleFeatureSelect"
              @feature-modify="handleFeatureModify"
              @clear="handleDrawClear"
            />
          </div>
        </div>

        <div class="component-demo-section">
          <h4>MapDrawDialog 组件演示</h4>
          <div class="dialog-controls">
            <el-button type="primary" @click="openMapDrawDialog">
              打开地图绘制弹窗
            </el-button>
            <el-button type="info" @click="openMapDrawWithData" :disabled="!drawWKTData">
              使用已有数据打开弹窗
            </el-button>
          </div>
          
          <MapDrawDialog
            v-model:visible="drawDialogVisible"
            v-model:model-value="dialogWKTData"
            title="地图绘制弹窗演示"
            width="90%"
            height="80%"
            :reference-data="referenceWKTData"
            :allow-multiple="allowMultipleShapes"
            :center="center"
            :zoom="zoom"
            :projection="projection"
            :tianditu-token="tiandituToken"
            @confirm="handleDialogConfirm"
            @draw-end="handleDialogDrawEnd"
          />
        </div>

        <!-- 控制选项 -->
        <div class="draw-controls">
          <div class="control-item">
            <el-checkbox v-model="allowMultipleShapes">允许绘制多个图形</el-checkbox>
          </div>
          <div class="control-item">
            <el-button size="small" @click="addReferenceData">添加参考数据</el-button>
            <el-button size="small" @click="clearReferenceData" type="warning">清空参考数据</el-button>
          </div>
          <div class="control-item">
            <el-button size="small" @click="exportWKTData" :disabled="!drawWKTData">导出WKT数据</el-button>
            <el-button size="small" @click="importWKTData">导入WKT数据</el-button>
          </div>
        </div>

        <!-- 数据显示区域 -->
        <div class="data-display">
          <div class="data-section">
            <h4>当前绘制数据 (WKT)</h4>
            <el-input
              v-model="drawWKTData"
              type="textarea"
              :rows="4"
              placeholder="绘制数据将显示在这里..."
              readonly
            />
          </div>
          
          <div class="data-section">
            <h4>弹窗绘制数据 (WKT)</h4>
            <el-input
              v-model="dialogWKTData"
              type="textarea"
              :rows="4"
              placeholder="弹窗绘制数据将显示在这里..."
              readonly
            />
          </div>

          <div class="data-section">
            <h4>参考数据 (WKT)</h4>
            <el-input
              v-model="referenceDataText"
              type="textarea"
              :rows="3"
              placeholder="参考数据..."
              readonly
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 右侧详情面板 -->
    <div v-if="selectedFeature" class="details-panel" :class="{ 'panel-visible': selectedFeature }">
      <div class="panel-header">
        <h3>地块详情</h3>
        <button @click="closePanel" class="close-btn">&times;</button>
      </div>
      <div class="panel-content">
        <div class="detail-item">
          <span class="label">序号:</span>
          <span>{{ selectedFeature.get('serial_number') }}</span>
        </div>
        <div class="detail-item">
          <span class="label">名称:</span>
          <span>{{ selectedFeature.get('name') }}</span>
        </div>
        <div class="detail-item">
          <span class="label">用地类型:</span>
          <span>{{ selectedFeature.get('layer') }}</span>
        </div>
        <div class="detail-item">
          <span class="label">面积:</span>
          <span>{{ selectedFeature.get('area') }} 平方米</span>
        </div>
        <!-- 可以添加更多属性 -->
      </div>
    </div>

    <!-- 数据导入弹窗 -->
    <el-dialog v-model="importDialogVisible" title="导入WKT数据" width="500px">
      <el-form>
        <el-form-item label="WKT数据">
          <el-input
            v-model="importWKTText"
            type="textarea"
            :rows="6"
            placeholder="请输入WKT格式的数据..."
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="importDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmImport">导入</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, inject } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { QuestionFilled } from '@element-plus/icons-vue'
import { Style, Fill, Stroke, Text } from 'ol/style'
import { createStringXY } from 'ol/coordinate'
import MapDrawCore from '@/components/MapDraw/src/MapDrawCore.vue'
import MapDrawDialog from '@/components/MapDraw/src/MapDrawDialog.vue'

//延吉市位置
const center = ref([129.5040, 42.9156])
const projection = ref('EPSG:4326')
const zoom = ref(13)
const view = ref()
const selectedFeature = ref(null)
const vectorLayer = ref(null)
const tiandituToken = '4989e906aa138e5bb1b49a3eb83a6128'

// 帮助弹窗相关
const helpDialogVisible = ref(false)

// 地图绘制相关数据
const isDrawMode = ref(false)
const drawWKTData = ref('')
const dialogWKTData = ref('')
const referenceWKTData = ref([])
const allowMultipleShapes = ref(true)
const drawDialogVisible = ref(false)

// 数据导入相关
const importDialogVisible = ref(false)
const importWKTText = ref('')

// 帮助文档数据
const propsData = ref([
  { prop: 'modelValue', type: 'String', default: "''", description: 'WKT格式的几何数据，支持v-model双向绑定' },
  { prop: 'referenceData', type: 'Array', default: '[]', description: '参考数据数组，WKT格式字符串数组' },
  { prop: 'showToolbar', type: 'Boolean', default: 'true', description: '是否显示绘制工具栏' },
  { prop: 'allowedModes', type: 'Array', default: "['Point', 'LineString', 'Polygon']", description: '允许的绘制模式' },
  { prop: 'allowMultiple', type: 'Boolean', default: 'false', description: '是否允许绘制多个图形' },
  { prop: 'center', type: 'Array', default: '[116.404, 39.915]', description: '地图中心点坐标' },
  { prop: 'zoom', type: 'Number', default: '10', description: '地图初始缩放级别' },
  { prop: 'projection', type: 'String', default: "'EPSG:4326'", description: '坐标系统' },
  { prop: 'readonly', type: 'Boolean', default: 'false', description: '是否为只读模式' },
  { prop: 'height', type: 'String', default: "'400px'", description: '组件高度' },
  { prop: 'width', type: 'String', default: "'100%'", description: '组件宽度' }
])

const eventsData = ref([
  { event: 'update:modelValue', params: 'wkt: string', description: '绘制数据变化时触发' },
  { event: 'draw-start', params: 'event: {mode, feature}', description: '开始绘制时触发' },
  { event: 'draw-end', params: 'event: {mode, feature, wkt}', description: '绘制完成时触发' },
  { event: 'feature-select', params: 'event: {feature, wkt}', description: '选择要素时触发' },
  { event: 'feature-modify', params: 'event: {feature, wkt}', description: '修改要素时触发' },
  { event: 'feature-delete', params: 'event: {feature, wkt}', description: '删除要素时触发' },
  { event: 'clear', params: '-', description: '清空所有要素时触发' }
])

// 计算属性
const referenceDataText = computed(() => {
  return referenceWKTData.value.join('\n')
})

// 处理选择事件
const handleSelect = (event) => {
  const selected = event.selected[0]
  selectedFeature.value = selected || null
}

// 关闭详情面板
const closePanel = () => {
  selectedFeature.value = null
}

// 选择过滤器
const selectFilter = (feature) => {
  return feature.get('layer') !== undefined
}

// 地图数据相关方法
const urlFunction = (extent, resolution, projection) => {
  const proj = projection.getCode()
  const url = '';
  // const url =
  //   'http://************:9090/geoserver/wfs?service=WFS&' +
  //   'version=1.1.0&request=GetFeature&typename=gzvr:gzvr_land_type&' +
  //   'outputFormat=application/json&srsname=' +
  //   proj +
  //   '&' +
  //   'bbox=' +
  //   extent.join(',') +
  //   ',' +
  //   proj
  return url
}

// vue3-openlayers 注入的依赖
const strategy = inject('ol-loadingstrategy')
const format = inject('ol-format')
const bbox = strategy?.bbox
const GeoJSON = format ? new format.GeoJSON() : null

const coordinateFormat = createStringXY(4)

const getZoomLevel = (resolution) => {
  return Math.round(view.value?.getZoomForResolution(resolution) || 0)
}

// 地图绘制相关方法
const toggleDrawMode = () => {
  isDrawMode.value = !isDrawMode.value
  if (!isDrawMode.value) {
    // 退出绘制模式时清空数据
    drawWKTData.value = ''
    dialogWKTData.value = ''
  }
}

const handleDrawStart = (event) => {
  console.log('绘制开始:', event)
  ElMessage.info(`开始绘制${getModeText(event.mode)}`)
}

const handleDrawEnd = (event) => {
  console.log('绘制结束:', event)
  ElMessage.success(`${getModeText(event.mode)}绘制完成`)
}

const handleFeatureSelect = (event) => {
  console.log('要素选择:', event)
  if (event.feature) {
    ElMessage.info('已选择要素，可进行编辑')
  }
}

const handleFeatureModify = (event) => {
  console.log('要素修改:', event)
  ElMessage.success('要素修改完成')
}

const handleDrawClear = () => {
  console.log('清空绘制')
  ElMessage.warning('已清空所有绘制内容')
}

// 弹窗相关方法
const openMapDrawDialog = () => {
  dialogWKTData.value = ''
  drawDialogVisible.value = true
}

const openMapDrawWithData = () => {
  dialogWKTData.value = drawWKTData.value
  drawDialogVisible.value = true
}

const handleDialogConfirm = (wktData) => {
  console.log('弹窗确认:', wktData)
  ElMessage.success('弹窗绘制数据已确认')
}

const handleDialogDrawEnd = (event) => {
  console.log('弹窗绘制结束:', event)
}

// 参考数据相关方法
const addReferenceData = () => {
  // 添加一些示例参考数据
  const sampleData = [
    'POINT (129.504 42.916)',
    'LINESTRING (129.500 42.910, 129.510 42.920)',
    'POLYGON ((129.495 42.905, 129.505 42.905, 129.505 42.915, 129.495 42.915, 129.495 42.905))'
  ]
  
  referenceWKTData.value.push(...sampleData)
  ElMessage.success('已添加参考数据')
}

const clearReferenceData = () => {
  referenceWKTData.value = []
  ElMessage.success('已清空参考数据')
}

// 数据导入导出方法
const exportWKTData = async () => {
  if (!drawWKTData.value) {
    ElMessage.warning('没有可导出的数据')
    return
  }
  
  try {
    // 创建下载链接
    const blob = new Blob([drawWKTData.value], { type: 'text/plain' })
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `map-draw-data-${new Date().getTime()}.wkt`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)
    
    ElMessage.success('数据导出成功')
  } catch (error) {
    ElMessage.error('数据导出失败')
    console.error(error)
  }
}

const importWKTData = () => {
  importWKTText.value = ''
  importDialogVisible.value = true
}

const confirmImport = () => {
  if (!importWKTText.value.trim()) {
    ElMessage.warning('请输入WKT数据')
    return
  }
  
  try {
    // 简单验证WKT格式
    const wktRegex = /^(POINT|LINESTRING|POLYGON|MULTIPOINT|MULTILINESTRING|MULTIPOLYGON|GEOMETRYCOLLECTION)/i
    if (!wktRegex.test(importWKTText.value.trim())) {
      throw new Error('无效的WKT格式')
    }
    
    drawWKTData.value = importWKTText.value.trim()
    importDialogVisible.value = false
    ElMessage.success('数据导入成功')
  } catch (error) {
    ElMessage.error('数据导入失败: ' + error.message)
  }
}

// 辅助方法
const getModeText = (mode) => {
  const modeTexts = {
    'Point': '点',
    'LineString': '线',
    'Polygon': '面'
  }
  return modeTexts[mode] || mode
}

// 原有的样式函数
const styleFunction = (feature, currentStyle, resolution) => {
  const zoomLevel = getZoomLevel(resolution)
  const layer = feature.get('layer')
  const name = feature.get('name')
  const LABEL_RESOLUTION_THRESHOLD = 17
  // 根据layer属性匹配样式
  const styles = {
    'YD-二类居住用地-R2': {fill: '#ffff00',stroke: '#232323',width: 1},
    'YD-防护绿地-G2': {fill: '#008000',stroke: '#232323',width: 1},
    'YD-公园绿地-G1': {fill: '#00ff00',stroke: '#232323',width: 1},
    'YD-供电用地-U12': {fill: '#9370db',stroke: '#232323',width: 1},
    'YD-广场用地-G3': {fill: '#90ee90',stroke: '#232323',width: 1},
    'YD-加油加气站用地-B41': {fill: '#ffc0cb',stroke: '#232323',width: 1},
    'YD-交通场站用地-S4': {fill: '#c0c0c0',stroke: '#232323',width: 1},
    'YD-交通枢纽用地-S3': {fill: '#d6d6d6',stroke: '#232323',width: 1},
    'YD-教育科研用地-A3': {fill: '#ff7fff',stroke: '#232323',width: 1},
    'YD-军事用地-H41': {fill: '#2f4c26',stroke: '#232323',width: 1},
    'YD-商业用地-B1': {fill: '#ff0000',stroke: '#232323',width: 1},
    'YD-铁路用地-H21': {fill: '#667fcc',stroke: '#232323',width: 1},
    'YD-通信用地-U15': {fill: '#007299',stroke: '#232323',width: 1},
    'YD-文化设施用地-A2': {fill: '#ff9f7f',stroke: '#232323',width: 1},
    'YD-文物古迹用地-A7': {fill: '#cc3300',stroke: '#232323',width: 1},
    'YD-消防用地-U31': {fill: '#3f5f7f',stroke: '#232323',width: 1},
    'YD-行政办公用地-A1': {fill: '#ff00ff',stroke: '#232323',width: 1},
    'YD-中小学用地-A33': {fill: '#ff7fff',stroke: '#232323',width: 1},
    'YD-宗教用地-A9': {fill: '#7f003f',stroke: '#232323',width: 1}
  }

  // 获取当前feature的样式配置
  const style = styles[layer] || {
    fill: '#abe75b',
    stroke: '#232323',
    width: 1
  }

  return new Style({
    fill: new Fill({
      color: style.fill
    }),
    stroke: new Stroke({
      color: style.stroke,
      width: style.width
    }),
    text:
      zoomLevel > LABEL_RESOLUTION_THRESHOLD
        ? new Text({
          text: name || '',
          font: '14px Arial',
          fill: new Fill({
            color: '#000000'
          }),
          stroke: new Stroke({
            color: '#ffffff',
            width: 3
          }),
          overflow: true,
          offsetY: 0,
          textAlign: 'center',
          textBaseline: 'middle',
          padding: [2, 2, 2, 2]
        })
        : null
  })
}
</script>
<style>
.map-container {
  position: relative;
  width: 100%;
  height: 84vh;
}

/* 地图绘制工具栏样式 */
.map-draw-toolbar {
  position: absolute;
  top: 20px;
  right: 60px;
  z-index: 1000;
  background: rgba(255, 255, 255, 0.98);
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  max-width: 420px;
  max-height: calc(100vh - 60px);
  overflow-y: auto;
  backdrop-filter: blur(8px);
}

.toolbar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #f8f9fa;
  border-bottom: 1px solid #e4e7ed;
  border-radius: 8px 8px 0 0;
}

.toolbar-header h3 {
  margin: 0;
  font-size: 16px;
  color: #303133;
  font-weight: 600;
}

.draw-component-area {
  padding: 16px;
}

.component-demo-section {
  margin-bottom: 24px;
}

.component-demo-section:last-child {
  margin-bottom: 0;
}

.component-demo-section h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  color: #409eff;
  font-weight: 600;
  padding-bottom: 8px;
  border-bottom: 2px solid #ecf5ff;
}

.draw-container {
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  overflow: hidden;
}

.dialog-controls {
  display: flex;
  gap: 8px;
  margin-bottom: 16px;
  flex-wrap: wrap;
}

.draw-controls {
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 16px;
  background: #fafafa;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  margin-bottom: 16px;
}

.control-item {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.data-display {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.data-section h4 {
  margin: 0 0 8px 0;
  font-size: 13px;
  color: #606266;
  font-weight: 500;
}

.details-panel {
  position: absolute;
  top: 0;
  right: 0;
  width: 300px;
  height: 100%;
  background: white;
  box-shadow: -2px 0 5px rgba(0, 0, 0, 0.1);
  transform: translateX(100%);
  transition: transform 0.3s ease;
  z-index: 999;
}

.panel-visible {
  transform: translateX(0);
}

.panel-header {
  padding: 15px;
  background: #f5f5f5;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #ddd;
}

.panel-header h3 {
  margin: 0;
  font-size: 16px;
}

.close-btn {
  background: none;
  border: none;
  font-size: 20px;
  cursor: pointer;
  padding: 0 5px;
}

.panel-content {
  padding: 15px;
}

.detail-item {
  margin-bottom: 10px;
}

.detail-item .label {
  font-weight: bold;
  margin-right: 10px;
}

.custom-mouse-position {
  position: absolute !important;
  bottom: 8px !important;
  right: 10px !important;
  color: #fff;
  background-color: rgba(0, 0, 0, 0.5);
  padding: 5px 10px;
  border-radius: 4px;
  font-size: 14px;
  z-index: 1000;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .map-draw-toolbar {
    left: 10px;
    right: 10px;
    max-width: none;
    width: auto;
  }
  
  .dialog-controls {
    flex-direction: column;
  }
  
  .control-item {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .details-panel {
    width: 280px;
  }
}

/* 滚动条样式 */
.map-draw-toolbar::-webkit-scrollbar {
  width: 6px;
}

.map-draw-toolbar::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.map-draw-toolbar::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.map-draw-toolbar::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 帮助按钮样式 */
.help-button {
  position: absolute;
  top: 20px;
  right: 20px;
  z-index: 1001;
}

/* 帮助内容样式 */
.help-content {
  max-height: 70vh;
  overflow-y: auto;
}

.help-section {
  margin-bottom: 24px;
  padding: 16px;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  background: #fafbfc;
}

.help-section h3 {
  margin: 0 0 16px 0;
  color: #409eff;
  font-size: 18px;
  font-weight: 600;
}

.help-section h4 {
  margin: 0 0 12px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
  border-bottom: 2px solid #409eff;
  padding-bottom: 6px;
}

.help-section p {
  margin: 0 0 12px 0;
  color: #606266;
  line-height: 1.6;
}

.help-section ul,
.help-section ol {
  margin: 0;
  padding-left: 20px;
  color: #606266;
  line-height: 1.6;
}

.help-section li {
  margin-bottom: 6px;
}

.help-section pre {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  padding: 16px;
  margin: 12px 0;
  overflow-x: auto;
  font-size: 13px;
  line-height: 1.4;
}

.help-section code {
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  color: #e83e8c;
  background: #f8f9fa;
  padding: 2px 4px;
  border-radius: 3px;
  font-size: 12px;
}

.help-section pre code {
  background: none;
  color: #333;
  padding: 0;
}

/* 响应式设计 - 帮助弹窗 */
@media (max-width: 768px) {
  .help-button {
    right: 10px;
  }
  
  .help-content {
    max-height: 60vh;
  }
  
  .help-section {
    padding: 12px;
    margin-bottom: 16px;
  }
  
  .help-section pre {
    padding: 12px;
    font-size: 12px;
  }
}

/* 表格响应式 */
@media (max-width: 768px) {
  .help-section .el-table {
    font-size: 12px;
  }
}
</style>
