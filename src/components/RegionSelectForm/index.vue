<template>
  <div class="region-select-form" v-loading="formLoading" :style="{ height: props.height }">
    <el-row :gutter="20" class="full-height">
      <!-- 左侧树形结构 -->
      <el-col :span="12" class="full-height">
        <div class="tree-panel">
          <div class="panel-header">
            <span class="panel-title">区域树</span>
          </div>
          <div class="tree-search">
            <el-input
              v-model="searchKeyword"
              placeholder="搜索区域"
              :prefix-icon="Search"
              clearable
              @input="handleSearch"
            />
          </div>
          <div class="tree-content">
            <div v-if="regionTree.length === 0" class="tree-empty">
              暂无数据
            </div>
            <el-tree
              v-else
              ref="treeRef"
              :data="regionTree"
              :props="treeProps"
              :load="loadRegionTree"
              lazy
              show-checkbox
              node-key="id"
              :check-strictly="false"
              :filter-node-method="filterNode"
              @check="handleTreeCheck"
            />
          </div>
        </div>
      </el-col>

      <!-- 右侧已选区域 -->
      <el-col :span="12" class="full-height">
        <div class="selected-panel">
          <div class="panel-header">
            <span class="panel-title">已选区域 ({{ selectedRegions.length }})</span>
            <el-button
              v-if="selectedRegions.length > 0"
              size="small"
              @click="clearAll"
            >
              清空
            </el-button>
          </div>
          <div class="selected-content">
            <div v-if="selectedRegions.length === 0" class="selected-empty">
              暂无选择
            </div>
            <div v-else class="selected-list">
              <div
                v-for="region in selectedRegions"
                :key="region.id"
                class="selected-item"
              >
                <span class="item-name">{{ region.name }}</span>
                <span class="item-level">({{ levelNameMap[region.regionLevel] }})</span>
                <el-button
                  size="small"
                  type="danger"
                  link
                  @click="removeRegion(region.id)"
                >
                  移除
                </el-button>
              </div>
            </div>
          </div>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, watch, onMounted, nextTick } from 'vue'
import { Search } from '@element-plus/icons-vue'
import { RegionApi, RegionVO } from '@/api/urban/region'

// 组件选项
defineOptions({ name: 'RegionSelectForm' })

// 组件属性接口
interface RegionSelectFormProps {
  /** 当前选中的区域列表，支持 v-model */
  modelValue?: RegionVO[]
  /** 根节点父级编码，默认为 '-99' */
  rootParentCode?: string | number
  /** 可选择的级别数组，如 [7, 8] 表示只能选择社区和小区 */
  selectableLevel?: number[]
  /** 组件高度，默认为 '400px' */
  height?: string
  /** 是否支持多选，默认为 true */
  multiple?: boolean
  /** 占位符文本 */
  placeholder?: string
}

// 组件属性
const props = withDefaults(defineProps<RegionSelectFormProps>(), {
  modelValue: () => [],
  rootParentCode: '220000',
  selectableLevel: () => [3, 4, 5, 6, 7, 8], // 默认所有级别都可选
  height: '400px',
  multiple: true,
  placeholder: '请选择区域'
})

// 计算属性：确保 rootParentCode 是字符串
const rootParentCodeStr = computed(() => String(props.rootParentCode))

// 组件事件
const emit = defineEmits<{
  'update:modelValue': [selectedRegions: RegionVO[]]
  change: [selectedRegions: RegionVO[]]
}>()

// 响应式数据
const formLoading = ref(false)
const regionTree = ref<RegionVO[]>([])
const allRegionList = ref<RegionVO[]>([])
const selectedRegionIdList = ref<string[]>([])
const searchKeyword = ref('')
const treeLoadedCache = ref(new Map<string, RegionVO[]>())
const treeRef = ref()

// 级别名称映射
const levelNameMap = {
  3: '省',
  4: '市',
  5: '县区',
  6: '镇街',
  7: '社区村',
  8: '小区'
}

// 树形组件属性配置
const treeProps = {
  label: 'name',
  children: 'children',
  isLeaf: 'leaf'
}

// 计算属性：已选择的区域对象
const selectedRegions = computed(() => {
  return allRegionList.value.filter(region =>
    selectedRegionIdList.value.includes(region.id)
  )
})

// 判断区域是否可选择
const isRegionSelectable = (region: RegionVO): boolean => {
  return props.selectableLevel.includes(region.regionLevel)
}

// 判断节点是否应该显示为叶子节点
const shouldBeLeaf = (region: RegionVO): boolean => {
  // 如果已经是最大级别（小区），则为叶子节点
  if (region.regionLevel >= 8) {
    return true
  }

  // 检查下一级是否在可选择范围内
  const nextLevel = region.regionLevel + 1
  const hasSelectableNextLevel = props.selectableLevel.some(level => level >= nextLevel)

  // 如果下一级及以后的级别都不在可选择范围内，则当前节点为叶子节点
  return !hasSelectableNextLevel
}

// 懒加载区域树节点
const loadRegionTree = async (node: any, resolve: (data: RegionVO[]) => void) => {
  try {
    // 确定要加载的 parentCode
    let parentCode = rootParentCodeStr.value
    if (node && node.level > 0) {
      parentCode = node.data.code
    }

    // 检查缓存中是否已有数据
    if (treeLoadedCache.value.has(parentCode)) {
      const cachedData = treeLoadedCache.value.get(parentCode)!
      resolve(cachedData)
      return
    }

    // 从API加载数据
    const data = await RegionApi.getRegionList({ parentCode })
    const nodes: RegionVO[] = data.map(item => ({
      ...item,
      leaf: shouldBeLeaf(item), // 根据可选择级别判断是否为叶子节点
      disabled: !isRegionSelectable(item) // 不可选择的节点禁用复选框
    }))

    // 更新缓存
    treeLoadedCache.value.set(parentCode, nodes)

    // 将可选择的节点添加到全部区域列表中
    const selectableNodes = nodes.filter(isRegionSelectable)
    selectableNodes.forEach(node => {
      if (!allRegionList.value.find(r => r.id === node.id)) {
        allRegionList.value.push(node)
      }
    })

    resolve(nodes)
  } catch (error) {
    console.error('加载区域节点失败', error)
    resolve([])
  }
}



// 处理树节点选择
const handleTreeCheck = (data: RegionVO, checkInfo: any) => {
  // 获取所有选中的节点（包括半选中的父节点的子节点）
  const checkedNodes = treeRef.value.getCheckedNodes()
  const checkedIds = checkedNodes
    .filter((node: RegionVO) => isRegionSelectable(node))
    .map((node: RegionVO) => node.id)

  selectedRegionIdList.value = checkedIds

  // 获取选中的区域对象
  const selectedRegions = allRegionList.value.filter(region =>
    checkedIds.includes(region.id)
  )

  // 发射事件
  emit('update:modelValue', selectedRegions)
  emit('change', selectedRegions)
}

// 移除单个区域
const removeRegion = (regionId: string) => {
  const index = selectedRegionIdList.value.indexOf(regionId)
  if (index > -1) {
    selectedRegionIdList.value.splice(index, 1)

    // 同步更新树的选中状态
    nextTick(() => {
      if (treeRef.value) {
        treeRef.value.setCheckedKeys(selectedRegionIdList.value)
      }
    })

    // 发射事件
    emit('update:modelValue', selectedRegions.value)
    emit('change', selectedRegions.value)
  }
}

// 清空所有选择
const clearAll = () => {
  selectedRegionIdList.value = []

  // 同步更新树的选中状态
  nextTick(() => {
    if (treeRef.value) {
      treeRef.value.setCheckedKeys([])
    }
  })

  // 发射事件
  emit('update:modelValue', [])
  emit('change', [])
}

// 搜索功能
const handleSearch = (keyword: string) => {
  if (treeRef.value) {
    treeRef.value.filter(keyword)
  }
}

// 树节点过滤方法
const filterNode = (value: string, data: RegionVO) => {
  if (!value) return true
  return data.name.includes(value)
}

// 加载初始数据
const loadInitialData = async () => {
  try {
    formLoading.value = true

    console.log('开始加载初始数据，rootParentCode:', rootParentCodeStr.value)
    console.log('可选择的级别:', props.selectableLevel)

    // 加载根节点数据到树形结构
    const rootData = await RegionApi.getRegionList({ parentCode: rootParentCodeStr.value })
    console.log('API 返回的根节点数据:', rootData)

    regionTree.value = rootData.map(item => ({
      ...item,
      leaf: shouldBeLeaf(item),
      disabled: !isRegionSelectable(item)
    }))

    console.log('处理后的树形数据:', regionTree.value)
    console.log('叶子节点判断示例:', {
      regionLevel: rootData[0]?.regionLevel,
      shouldBeLeaf: shouldBeLeaf(rootData[0]),
      isSelectable: isRegionSelectable(rootData[0])
    })

    // 缓存根节点数据
    treeLoadedCache.value.set(rootParentCodeStr.value, regionTree.value)

    // 将可选择的根节点添加到全部区域列表（用于 transfer）
    const selectableRootRegions = rootData.filter(isRegionSelectable)
    console.log('可选择的根节点:', selectableRootRegions)

    selectableRootRegions.forEach(region => {
      if (!allRegionList.value.find(r => r.id === region.id)) {
        allRegionList.value.push(region)
      }
    })

    // 注意：即使根节点不可选择，也要显示在树中，用户需要展开查看子节点

    // 处理预设选中项
    if (props.modelValue && props.modelValue.length > 0) {
      props.modelValue.forEach(region => {
        if (isRegionSelectable(region) && !allRegionList.value.find(r => r.id === region.id)) {
          allRegionList.value.push(region)
        }
      })
    }

    console.log('最终的 allRegionList:', allRegionList.value)
    console.log('selectedRegions:', selectedRegions.value)
    console.log('regionTree.value:', regionTree.value)
    console.log(`已加载根节点数据，共 ${regionTree.value.length} 个节点，可选择 ${allRegionList.value.length} 个`)
  } catch (error) {
    console.error('加载初始数据失败', error)
  } finally {
    formLoading.value = false
  }
}

// 初始化选中状态
const initializeSelection = () => {
  if (props.modelValue && props.modelValue.length > 0) {
    // 添加已选择的区域到全部列表
    props.modelValue.forEach(region => {
      if (isRegionSelectable(region) && !allRegionList.value.find(r => r.id === region.id)) {
        allRegionList.value.push(region)
      }
    })

    // 设置选中状态
    const selectedIds = props.modelValue
      .filter(isRegionSelectable)
      .map(region => region.id)

    selectedRegionIdList.value = selectedIds

    // 同步树的选中状态
    nextTick(() => {
      if (treeRef.value && selectedIds.length > 0) {
        treeRef.value.setCheckedKeys(selectedIds)
      }
    })
  }
}

// 重置表单
const resetForm = () => {
  regionTree.value = []
  allRegionList.value = []
  selectedRegionIdList.value = []
  searchKeyword.value = ''
  treeLoadedCache.value.clear()
}

// 监听 modelValue 变化
watch(() => props.modelValue, (newValue) => {
  if (newValue) {
    initializeSelection()
  } else {
    selectedRegionIdList.value = []
  }
}, { deep: true, immediate: true })

// 监听 selectableLevel 变化
watch(() => props.selectableLevel, async () => {
  console.log('selectableLevel 变化，重新初始化组件')
  // 清空当前数据
  resetForm()
  // 重新加载初始数据
  await loadInitialData()
  // 重新初始化选择状态
  initializeSelection()
}, { deep: true })

// 组件挂载时初始化
onMounted(async () => {
  await loadInitialData()
  initializeSelection()
})

// 暴露方法
defineExpose({
  /** 重新加载数据 */
  reload: loadInitialData,
  /** 清空选择 */
  clear: () => {
    selectedRegionIdList.value = []
    if (treeRef.value) {
      treeRef.value.setCheckedKeys([])
    }
    emit('update:modelValue', [])
    emit('change', [])
  }
})
</script>

<style lang="scss" scoped>
.region-select-form {
  width: 100%;
  display: flex;
  flex-direction: column;
}

.full-height {
  height: 100%;
}

.tree-panel,
.selected-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
  border: 1px solid #ebeef5;
  border-radius: 4px;
}

.panel-header {
  padding: 12px 16px;
  background: #f5f7fa;
  border-bottom: 1px solid #ebeef5;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-shrink: 0; /* 防止头部被压缩 */
}

.panel-title {
  font-weight: 500;
  color: #303133;
}

.tree-search {
  padding: 12px 16px;
  border-bottom: 1px solid #ebeef5;
  flex-shrink: 0; /* 防止搜索框被压缩 */
}

.tree-content,
.selected-content {
  flex: 1;
  overflow-y: auto; /* 只允许垂直滚动 */
  padding: 12px;
  min-height: 0; /* 确保可以缩小 */
}

.tree-empty,
.selected-empty {
  text-align: center;
  color: #909399;
  padding: 40px 0;
}

.selected-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.selected-item {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  background: #f0f9ff;
  border: 1px solid #b3d8ff;
  border-radius: 4px;
  gap: 8px;
  flex-shrink: 0; /* 防止选项被压缩 */
}

.item-name {
  flex: 1;
  color: #303133;
}

.item-level {
  color: #909399;
  font-size: 12px;
}

:deep() {
  .el-tree {
    background: transparent;
  }

  .el-tree-node__content {
    height: 32px;
  }

  .el-row {
    height: 100%;
  }

  .el-col {
    height: 100%;
  }
}
</style>
