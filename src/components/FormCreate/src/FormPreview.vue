<template>
  <div class="form-preview-container">
    <!-- 加载状态 -->
    <div v-if="loading" class="loading-state">
      <el-skeleton :rows="5" animated />
    </div>

    <!-- 错误状态 -->
    <div v-else-if="error" class="error-state">
      <Icon icon="ep:warning" size="48" color="#f56c6c" />
      <p>加载表单配置失败</p>
      <p class="error-desc">{{ error }}</p>
      <el-button @click="loadFormConfig" type="primary" size="small">重试</el-button>
    </div>

    <!-- 无数据状态 -->
    <div v-else-if="!hasFormConfig" class="no-data-state">
      <Icon icon="ep:document-add" size="48" color="#e6a23c" />
      <p>暂无采集表单配置</p>
      <p class="no-data-desc">该指标还没有配置采集表单，请先创建表单配置</p>
      <el-button @click="handleAddForm" type="primary" size="small">
        <Icon icon="ep:plus" class="mr-5px" />
        新增表单配置
      </el-button>
    </div>

    <!-- 表单预览 -->
    <div v-else class="preview-content">
      <div class="preview-header">
        <div class="header-left">
          <h4>表单预览</h4>
          <span class="version-info">版本 {{ formData.formVersion || 1 }}</span>
        </div>
        <div class="header-right">
          <el-button @click="handleEditForm" type="primary" size="small" link>
            <Icon icon="ep:edit" class="mr-5px" />
            编辑配置
          </el-button>
        </div>
      </div>

      <div class="preview-body">
        <form-create
          v-if="previewRule.length > 0"
          :rule="previewRule"
          :option="previewOption"
          :value="previewValue"
        />
        <div v-else class="empty-form">
          <Icon icon="ep:document" size="32" color="#c0c4cc" />
          <p>表单配置为空</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { CitystandardItemFormApi, CitystandardItemFormVO } from '@/api/urban/citystandarditemform'
import formCreate from '@form-create/element-ui'

/** 表单预览组件 */
defineOptions({ name: 'FormPreview' })

// Props
interface Props {
  indicatorId: number | undefined
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'add-form': []
  'edit-form': [formData: CitystandardItemFormVO]
}>()

// 响应式数据
const loading = ref(false)
const error = ref('')
const formData = ref<CitystandardItemFormVO | null>(null)
const previewRule = ref([])
const previewOption = ref({
  submitBtn: false,
  resetBtn: false,
  form: {
    labelWidth: '120px',
    size: 'default'
  }
})
const previewValue = ref({})

// 计算属性
const hasFormConfig = computed(() => {
  return formData.value && formData.value.formRule
})

/** 加载表单配置 */
const loadFormConfig = async () => {
  if (!props.indicatorId) return

  loading.value = true
  error.value = ''

  try {
    // 根据指标ID查询表单配置
    const data = await CitystandardItemFormApi.getCitystandardItemFormPage({
      itemId: props.indicatorId,
      pageNo: 1,
      pageSize: 10
    })

    if (data && data.list && data.list.length > 0) {
      // 取最新版本的配置
      formData.value = data.list[0]
      setupPreviewData()
    } else {
      formData.value = null
      resetPreviewData()
    }
  } catch (err: any) {
    error.value = err.message || '加载失败'
    console.error('加载表单配置失败:', err)
  } finally {
    loading.value = false
  }
}

/** 设置预览数据 */
const setupPreviewData = () => {
  if (!formData.value) return

  try {
    // 解析表单规则
    let rule = formData.value.formRule
    if (typeof rule === 'string') {
      rule = JSON.parse(rule)
    }
    previewRule.value = Array.isArray(rule) ? rule : []

    // 解析表单选项
    let option = formData.value.formOption
    if (typeof option === 'string') {
      option = JSON.parse(option)
    }
    previewOption.value = {
      ...previewOption.value,
      ...option,
      submitBtn: false,
      resetBtn: false
    }

    // 重置表单值
    previewValue.value = {}
  } catch (err) {
    console.error('解析表单配置失败:', err)
    error.value = '表单配置格式错误'
  }
}

/** 重置预览数据 */
const resetPreviewData = () => {
  previewRule.value = []
  previewValue.value = {}
}

/** 重置所有数据 */
const resetData = () => {
  formData.value = null
  error.value = ''
  resetPreviewData()
}

// 监听指标ID变化
watch(() => props.indicatorId, (newId) => {
  if (newId) {
    loadFormConfig()
  } else {
    resetData()
  }
}, { immediate: true })

/** 处理新增表单 */
const handleAddForm = () => {
  emit('add-form')
}

/** 处理编辑表单 */
const handleEditForm = () => {
  if (formData.value) {
    emit('edit-form', formData.value)
  }
}

// 暴露方法供父组件调用
defineExpose({
  refresh: loadFormConfig
})
</script>

<style lang="scss" scoped>
.form-preview-container {
  height: 100%;
  display: flex;
  flex-direction: column;

  .loading-state,
  .error-state,
  .no-data-state {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #909399;

    p {
      margin: 8px 0;
      font-size: 16px;
    }

    .error-desc,
    .no-data-desc {
      font-size: 14px;
      color: #c0c4cc;
      margin-bottom: 16px;
    }
  }

  .preview-content {
    height: 100%;
    display: flex;
    flex-direction: column;

    .preview-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 0 16px 0;
      border-bottom: 1px solid #e4e7ed;

      .header-left {
        display: flex;
        align-items: center;
        gap: 12px;

        h4 {
          margin: 0;
          font-size: 16px;
          font-weight: 500;
          color: #303133;
        }

        .version-info {
          font-size: 12px;
          color: #909399;
          background: #f0f2f5;
          padding: 2px 8px;
          border-radius: 4px;
        }
      }
    }

    .preview-body {
      flex: 1;
      padding: 20px 0;
      overflow-y: auto;

      .empty-form {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 200px;
        color: #909399;

        p {
          margin: 8px 0;
          font-size: 14px;
        }
      }
    }
  }
}
</style>
