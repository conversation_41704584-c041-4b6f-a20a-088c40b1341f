<template>
  <div class="task-layout">
    <!-- 左侧地图区域 -->
    <div class="map-section">
      <ol-map :loadTilesWhileAnimating="true" :loadTilesWhileInteracting="true" style="height: 100%">
        <ol-view ref="view" :center="center" :projection="projection" :zoom="zoom" />

        <ol-tile-layer>
          <ol-source-tianditu
            :hidpi="true"
            layerType="img"
            projection="EPSG:4326"
            tk="4989e906aa138e5bb1b49a3eb83a6128"
          />
        </ol-tile-layer>

        <ol-tile-layer>
          <ol-source-tianditu
            :hidpi="true"
            :isLabel="true"
            layerType="img"
            projection="EPSG:4326"
            tk="4989e906aa138e5bb1b49a3eb83a6128"
          />
        </ol-tile-layer>

        <!-- 任务数据图层 -->
        <ol-vector-layer ref="taskVectorLayer">
          <ol-source-vector ref="taskVectorSource" />
          <ol-style :overrideStyleFunction="taskStyleFunction" />
        </ol-vector-layer>

        <!-- 业务数据图层（楼栋、小区、社区、街区） -->
        <ol-vector-layer ref="businessVectorLayer">
          <ol-source-vector ref="businessVectorSource" />
          <ol-style :overrideStyleFunction="businessStyleFunction" />
        </ol-vector-layer>

        <ol-interaction-select
          :filter="taskSelectFilter"
          @select="handleTaskSelect"
        >
          <ol-style>
            <ol-style-stroke color="#1890ff" :width="4"/>
            <ol-style-fill color="rgba(24,144,255,0.3)"/>
          </ol-style>
        </ol-interaction-select>

        <ol-scaleline-control />
        <ol-mouseposition-control
          :projection="'EPSG:4326'"
          :coordinateFormat="coordinateFormat"
          className="custom-mouse-position"
        />
      </ol-map>
    </div>

    <!-- 右侧内容区域 -->
    <div class="content-section">
      <ContentWrap class="search-section">
        <!-- 搜索工作栏 -->
        <el-form
          class="-mb-15px"
          :model="queryParams"
          ref="queryFormRef"
          :inline="true"
          label-width="68px"
        >
          <!-- 保留的核心查询条件 -->
          <el-form-item label="任务编号" prop="taskId">
            <el-input
              v-model="queryParams.taskId"
              placeholder="任务编号"
              clearable
              @input="handleSearchInput"
              @clear="handleSearchClear"
              class="!w-100px"
            />
          </el-form-item>
          <el-form-item label="任务名称" prop="taskName">
            <el-input
              v-model="queryParams.taskName"
              placeholder="任务名称"
              clearable
              @input="handleSearchInput"
              @clear="handleSearchClear"
              class="!w-100px"
            />
          </el-form-item>
          
          <el-form-item>
            <el-button @click="showAdvancedSearch = true" size="small">
              <Icon icon="ep:operation" class="mr-5px" />高级查询
            </el-button>
            <el-button
              type="primary"
              plain
              @click="openForm('create')"
              v-hasPermi="['urban:task:create']"
              size="small"
            >
              <Icon icon="ep:plus" class="mr-5px" /> 新增
            </el-button>
            <el-button
              type="warning"
              plain
              @click="openBatchCreateDialog"
              v-hasPermi="['urban:task:create']"
              size="small"
            >
              <Icon icon="ep:collection-tag" class="mr-5px" /> 批量创建
            </el-button>
            <el-button
              type="success"
              plain
              @click="handleExport"
              :loading="exportLoading"
              v-hasPermi="['urban:task:export']"
              size="small"
            >
              <Icon icon="ep:download" class="mr-5px" /> 导出
            </el-button>
          </el-form-item>
        </el-form>
      </ContentWrap>

      <!-- 任务分类标签页 -->
      <ContentWrap class="tab-section">
        <el-tabs v-model="activeTab" @tab-change="handleTabChange" class="task-tabs">
          <el-tab-pane
            v-for="taskType in taskTypeOptions"
            :key="taskType.value"
            :label="taskType.label"
            :name="taskType.value"
          >
            <template #label>
              <div class="tab-label">
                <Icon :icon="getTabIcon(taskType.label)" />
                <span>{{ taskType.label }}</span>
                <el-badge :value="tabCounts[taskType.value] || 0" :max="99" class="tab-badge" />
              </div>
            </template>
          </el-tab-pane>
        </el-tabs>
      </ContentWrap>

      <!-- 任务列表 -->
      <ContentWrap class="table-section">
        <el-table
          ref="tableRef"
          v-loading="loading"
          :data="list"
          :stripe="true"
          :show-overflow-tooltip="true"
          :row-class-name="getRowClassName"
          @row-click="handleRowClick"
          highlight-current-row
          :height="tableHeight"
          :row-style="{ cursor: 'pointer' }"
        >
          <el-table-column label="任务编号" align="center" prop="taskId" width="180" />
          <el-table-column label="任务名称" align="center" prop="taskName" width="200" />
          <el-table-column label="状态" align="center" prop="status" width="100">
            <template #default="scope">
              <el-tag :type="getStatusTagType(scope.row.status)">
                {{ getStatusText(scope.row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="组长" align="center" prop="leaderName" width="100" />
          <el-table-column
            label="计划完成时间"
            align="center"
            prop="plannedTime"
            :formatter="dateFormatter"
            width="130"
          />
          <el-table-column label="操作" align="center" width="120" fixed="right">
            <template #default="scope">
              <el-button
                link
                type="primary"
                @click.stop="openForm('update', scope.row.id)"
                v-hasPermi="['urban:task:update']"
                size="small"
              >
                重新分配
              </el-button>
              <el-button
                link
                type="danger"
                @click.stop="handleDelete(scope.row.id)"
                v-hasPermi="['urban:task:delete']"
                size="small"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <!-- 分页 -->
        <Pagination
          :total="total"
          v-model:page="queryParams.pageNo"
          v-model:limit="queryParams.pageSize"
          @pagination="getList"
        />
      </ContentWrap>
    </div>

    <!-- 高级查询抽屉 -->
    <el-drawer
      v-model="showAdvancedSearch"
      title="高级查询"
      :size="400"
      direction="rtl"
    >
      <el-form :model="queryParams" label-width="100px">
        <el-form-item label="省" prop="province">
          <el-input
            v-model="queryParams.province"
            placeholder="请输入省"
            clearable
          />
        </el-form-item>
        <el-form-item label="市" prop="city">
          <el-input
            v-model="queryParams.city"
            placeholder="请输入市"
            clearable
          />
        </el-form-item>
        <el-form-item label="县区" prop="xzqdm">
          <el-input
            v-model="queryParams.xzqdm"
            placeholder="请输入县区"
            clearable
          />
        </el-form-item>
        <el-form-item label="镇街" prop="town">
          <el-input
            v-model="queryParams.town"
            placeholder="请输入镇街"
            clearable
          />
        </el-form-item>
        <el-form-item label="社区村" prop="village">
          <el-input
            v-model="queryParams.village"
            placeholder="请输入社区村"
            clearable
          />
        </el-form-item>
        <el-form-item label="小区" prop="community">
          <el-input
            v-model="queryParams.community"
            placeholder="请输入小区"
            clearable
          />
        </el-form-item>
        <el-form-item label="组长" prop="leaderName">
          <el-input
            v-model="queryParams.leaderName"
            placeholder="请输入组长"
            clearable
          />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select
            v-model="queryParams.status"
            placeholder="请选择状态"
            clearable
            class="!w-full"
          >
            <el-option
              v-for="dict in getIntDictOptions(DICT_TYPE.UC_TASK_STATUS)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="计划完成时间" prop="plannedTime">
          <el-date-picker
            v-model="queryParams.plannedTime"
            value-format="YYYY-MM-DD HH:mm:ss"
            type="daterange"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
            class="!w-full"
          />
        </el-form-item>
        <el-form-item label="实际完成时间" prop="actualDate">
          <el-date-picker
            v-model="queryParams.actualDate"
            value-format="YYYY-MM-DD HH:mm:ss"
            type="daterange"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
            class="!w-full"
          />
        </el-form-item>
        <el-form-item label="创建时间" prop="createTime">
          <el-date-picker
            v-model="queryParams.createTime"
            value-format="YYYY-MM-DD HH:mm:ss"
            type="daterange"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
            class="!w-full"
          />
        </el-form-item>
        <el-form-item label="图形" prop="geom">
          <el-input
            v-model="queryParams.geom"
            placeholder="请输入图形"
            clearable
          />
        </el-form-item>
        
        <div class="drawer-footer">
          <el-button @click="handleAdvancedQuery">确认查询</el-button>
          <el-button @click="resetAdvancedQuery">清空条件</el-button>
          <el-button @click="showAdvancedSearch = false">取消</el-button>
        </div>
      </el-form>
    </el-drawer>
  </div>

  <!-- 表单弹窗：添加/修改 -->
  <TaskForm ref="formRef" @success="getList" />

  <!-- 批量创建任务弹窗 -->
  <BatchCreateTaskDialog ref="batchCreateDialogRef" @success="getList" />
</template>

<script setup lang="ts">
import { ref, computed, inject, nextTick, onMounted } from 'vue'
import { getIntDictOptions, getStrDictOptions, DICT_TYPE } from '@/utils/dict'
import { dateFormatter } from '@/utils/formatTime'
import download from '@/utils/download'
import { TaskApi, TaskVO } from '@/api/urban/task'
import TaskForm from './TaskForm.vue'
import BatchCreateTaskDialog from './BatchCreateTaskDialog.vue'
import { Style, Fill, Stroke, Text } from 'ol/style'
import { createStringXY } from 'ol/coordinate'
import { WKT } from 'ol/format'
import { Feature } from 'ol'
import { Geometry } from 'ol/geom'

// 业务数据接口定义
interface BusinessData {
  id: number
  geom: string
  name?: string
  [key: string]: any
}

// 防抖函数
function debounce<T extends (...args: any[]) => any>(func: T, delay: number): T {
  let timeoutId: NodeJS.Timeout
  return ((...args: any[]) => {
    clearTimeout(timeoutId)
    timeoutId = setTimeout(() => func(...args), delay)
  }) as T
}

/** 任务管理 列表 */
defineOptions({ name: 'Task' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

// 地图相关
const center = ref([129.5040, 42.9156]) // 延吉市位置
const projection = ref('EPSG:4326')
const zoom = ref(13)
const view = ref()
const taskVectorLayer = ref() // 任务矢量图层
const taskVectorSource = ref() // 任务数据源
const businessVectorLayer = ref() // 业务数据图层（楼栋、小区、社区、街区）
const businessVectorSource = ref() // 业务数据源
const selectedTaskId = ref<number | null>(null) // 选中的任务ID
const mapInstance = ref() // 地图实例引用

// Tab相关状态
const activeTab = ref('') // 当前激活的tab，初始化时会设置为第一个字典项的值
const tabCounts = ref<Record<string, number>>({})

// vue3-openlayers 注入的依赖
const strategy = inject('ol-loadingstrategy')
const format = inject('ol-format')
const bbox = strategy?.bbox
const GeoJSON = format ? new format.GeoJSON() : null

const coordinateFormat = createStringXY(4)
const wktFormat = new WKT() // WKT格式解析器

const loading = ref(true) // 列表的加载中
const list = ref<TaskVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const showAdvancedSearch = ref(false) // 高级查询抽屉显示状态
const tableRef = ref() // 表格引用

// 获取任务类型字典选项
const taskTypeOptions = computed(() => getStrDictOptions(DICT_TYPE.UC_TASK_TYPE))

// 任务类型标签映射 - 根据字典value获取label
const getTaskTypeLabel = (value: string): string => {
  const option = taskTypeOptions.value.find(opt => opt.value === value)
  return option?.label || value
}

// 根据标签获取图标
const getTabIcon = (label: string): string => {
  switch (label) {
    case '住房':
      return 'ep:house'
    case '小区':
      return 'ep:menu'
    case '社区':
      return 'ep:office-building'
    case '街区':
      return 'ep:coordinate'
    default:
      return 'ep:document'
  }
}

// 计算表格高度
const tableHeight = computed(() => {
  // 计算可用高度：总高度 - 搜索区域高度 - tab高度 - 分页高度 - 边距
  return 'calc(100vh - 340px)'
})

const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  taskId: undefined,
  taskType: undefined as string | undefined, // 根据当前tab自动设置
  taskName: undefined,
  province: undefined,
  city: undefined,
  xzqdm: undefined,
  town: undefined,
  village: undefined,
  community: undefined,
  leaderName: undefined,
  status: undefined,
  plannedTime: [],
  actualDate: [],
  createTime: [],
  geom: undefined
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

// 获取实际的 VectorSource 对象
const getTaskVectorSource = () => {
  return taskVectorSource.value?.source || taskVectorSource.value
}

// 获取实际的地图视图对象
const getMapView = () => {
  return view.value?.map?.getView() || view.value
}

// 获取业务数据源
const getBusinessVectorSource = () => {
  return businessVectorSource.value?.source || businessVectorSource.value
}

// 状态相关方法
const getStatusText = (status: number) => {
  switch (status) {
    case -1:
      return '进行中'
    case 0:
      return '未开始'
    case 1:
      return '已完成'
    default:
      return '未知'
  }
}

const getStatusTagType = (status: number) => {
  switch (status) {
    case -1:
      return 'warning' // 黄色
    case 0:
      return 'danger' // 红色
    case 1:
      return 'success' // 绿色
    default:
      return 'info'
  }
}

// 根据状态获取地图样式颜色
const getStatusColor = (status: number) => {
  switch (status) {
    case -1:
      return { fill: 'rgba(230, 162, 60, 0.3)', stroke: '#E6A23C' } // 黄色（进行中）
    case 0:
      return { fill: 'rgba(245, 108, 108, 0.3)', stroke: '#F56C6C' } // 红色（未开始）
    case 1:
      return { fill: 'rgba(103, 194, 58, 0.3)', stroke: '#67C23A' } // 绿色（已完成）
    default:
      return { fill: 'rgba(144, 147, 153, 0.3)', stroke: '#909399' } // 灰色
  }
}

// 任务图层样式函数
const taskStyleFunction = (feature: any) => {
  const task = feature.get('task')
  const isSelected = feature.get('taskId') === selectedTaskId.value
  
  if (!task) {
    return new Style()
  }

  const colors = getStatusColor(task.status)
  
  return new Style({
    fill: new Fill({
      color: isSelected ? 'rgba(24, 144, 255, 0.5)' : colors.fill
    }),
    stroke: new Stroke({
      color: isSelected ? '#1890ff' : colors.stroke,
      width: isSelected ? 3 : 2
    }),
    text: new Text({
      text: task.taskName || task.taskId || '',
      font: '12px Arial',
      fill: new Fill({
        color: '#000000'
      }),
      stroke: new Stroke({
        color: '#ffffff',
        width: 2
      }),
      overflow: true,
      offsetY: 0,
      textAlign: 'center',
      textBaseline: 'middle'
    })
  })
}

// 业务数据图层样式函数
const businessStyleFunction = (feature: any) => {
  const task = feature.get('task')
  const isSelected = feature.get('taskId') === selectedTaskId.value
  
  if (!task) {
    return new Style()
  }

  const colors = getStatusColor(task.status)
  
  return new Style({
    fill: new Fill({
      color: isSelected ? 'rgba(230, 162, 60, 0.3)' : colors.fill
    }),
    stroke: new Stroke({
      color: isSelected ? '#E6A23C' : colors.stroke,
      width: isSelected ? 2 : 1
    }),
    text: new Text({
      text: task.taskName || task.taskId || '',
      font: '12px Arial',
      fill: new Fill({
        color: '#000000'
      }),
      stroke: new Stroke({
        color: '#ffffff',
        width: 2
      }),
      overflow: true,
      offsetY: 0,
      textAlign: 'center',
      textBaseline: 'middle'
    })
  })
}

// 任务选择过滤器
const taskSelectFilter = (feature: any) => {
  return feature.get('taskId') !== undefined
}

// 处理任务选择事件
const handleTaskSelect = (event: any) => {
  const selected = event.selected[0]
  if (selected) {
    const taskId = selected.get('taskId')
    selectedTaskId.value = taskId
    
    // 在列表中高亮对应行
    const task = list.value.find(t => t.id === taskId)
    if (task) {
      // 设置表格当前行
      nextTick(() => {
        tableRef.value?.setCurrentRow(task)
      })
      message.success(`已选中任务：${task.taskName || task.taskId}`)
    }
  } else {
    selectedTaskId.value = null
  }
}

// 列表行类名
const getRowClassName = ({ row }: { row: TaskVO }) => {
  return row.id === selectedTaskId.value ? 'selected-row' : ''
}

// 处理列表行点击
const handleRowClick = (row: TaskVO) => {
  selectedTaskId.value = row.id
  
  // 在地图中定位到对应要素
  const vectorSource = getTaskVectorSource()
  if (vectorSource && row.geom) {
    try {
      const features = vectorSource.getFeatures()
      const targetFeature = features.find((f: any) => f.get('taskId') === row.id)
      
      if (targetFeature) {
        const geometry = targetFeature.getGeometry()
        if (geometry) {
          // 定位到要素范围
          const extent = geometry.getExtent()
          const mapView = getMapView()
          
          if (mapView && mapView.fit) {
            mapView.fit(extent, {
              padding: [50, 50, 50, 50],
              duration: 1000
            })
          }
          
          // 触发样式更新
          if (taskVectorLayer.value?.vectorLayer?.changed) {
            taskVectorLayer.value.vectorLayer.changed()
          } else if (taskVectorLayer.value?.changed) {
            taskVectorLayer.value.changed()
          }
          
          message.success(`已定位到任务：${row.taskName || row.taskId}`)
        }
      }
    } catch (error) {
      console.warn('地图定位失败:', error)
      message.warning('地图定位失败，请检查任务地理数据')
    }
  }
}

// 加载任务数据到地图
const loadTasksToMap = () => {
  const vectorSource = getTaskVectorSource()
  if (!vectorSource) {
    console.warn('矢量数据源未初始化')
    return
  }
  
  try {
    // 清空现有要素
    vectorSource.clear()
    
    // 添加有地理数据的任务到地图
    list.value.forEach(task => {
      if (task.geom) {
        try {
          // 解析WKT数据
          const geometry = wktFormat.readGeometry(task.geom, {
            dataProjection: 'EPSG:4326',
            featureProjection: 'EPSG:4326'
          })
          
          // 创建要素
          const feature = new Feature({
            geometry: geometry,
            task: task,
            taskId: task.id,
            taskName: task.taskName,
            status: task.status
          })
          
          // 添加到数据源
          vectorSource.addFeature(feature)
        } catch (error) {
          console.warn(`任务 ${task.taskId} 的地理数据解析失败:`, error)
        }
      }
    })
    
    const featureCount = vectorSource.getFeatures().length
    console.log(`已加载 ${featureCount} 个任务到地图`)
    
    if (featureCount > 0) {
      // 可选：适配地图视图到所有要素
      const mapView = getMapView()
      if (mapView && mapView.fit) {
        const extent = vectorSource.getExtent()
        if (extent && extent.every(coord => isFinite(coord))) {
          setTimeout(() => {
            mapView.fit(extent, {
              padding: [50, 50, 50, 50],
              duration: 1000,
              maxZoom: 15
            })
          }, 500)
        }
      }
    }
  } catch (error) {
    console.error('加载任务数据到地图失败:', error)
  }
}

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    // 任务类型查询条件已经在 tab 切换时设置，这里不需要再设置

    const data = await TaskApi.getTaskPage(queryParams)
    list.value = data.list
    total.value = data.total

    // 更新当前tab的任务数量
    tabCounts.value[activeTab.value] = data.total

    // 加载数据到地图
    nextTick(() => {
      loadTasksToMap()
    })
  } finally {
    loading.value = false
  }
}

/** 高级查询确认 */
const handleAdvancedQuery = () => {
  queryParams.pageNo = 1
  selectedTaskId.value = null // 重置选中状态
  showAdvancedSearch.value = false
  getList()
}

/** 重置高级查询条件 */
const resetAdvancedQuery = () => {
  // 重置高级查询相关字段
  queryParams.province = undefined
  queryParams.city = undefined
  queryParams.xzqdm = undefined
  queryParams.town = undefined
  queryParams.village = undefined
  queryParams.community = undefined
  queryParams.leaderName = undefined
  queryParams.status = undefined
  queryParams.plannedTime = []
  queryParams.actualDate = []
  queryParams.createTime = []
  queryParams.geom = undefined
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  // 在新增模式下，传入当前激活的任务类型
  if (type === 'create') {
    formRef.value.open(type, id, activeTab.value)
  } else {
    formRef.value.open(type, id)
  }
}

/** 批量创建操作 */
const batchCreateDialogRef = ref()
const openBatchCreateDialog = () => {
  batchCreateDialogRef.value.open(activeTab.value)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await TaskApi.deleteTask(id)
    message.success(t('common.delSuccess'))
    // 重置选中状态
    if (selectedTaskId.value === id) {
      selectedTaskId.value = null
    }
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await TaskApi.exportTask(queryParams)
    download.excel(data, '任务管理.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 初始化 **/
onMounted(() => {
  // 等待字典数据加载完成后再设置初始查询类型
  nextTick(() => {
    if (taskTypeOptions.value.length > 0) {
      // 设置第一个字典项为默认激活tab
      activeTab.value = taskTypeOptions.value[0].value
      queryParams.taskType = activeTab.value

      // 初始化tab计数
      taskTypeOptions.value.forEach(option => {
        tabCounts.value[option.value] = 0
      })

      getList()
      // 加载初始业务数据
      loadBusinessData(activeTab.value)
    }
  })
})

// Tab切换处理
const handleTabChange = (tabName: string) => {
  activeTab.value = tabName
  queryParams.taskType = tabName // 直接使用字典的value值
  queryParams.pageNo = 1
  selectedTaskId.value = null

  // 重新查询数据
  getList()

  // 加载对应的业务数据
  loadBusinessData(tabName)
}

// 预留：加载业务数据方法
const loadBusinessData = async (tabType: string) => {
  const vectorSource = getBusinessVectorSource()
  if (!vectorSource) {
    console.warn('业务数据源未初始化')
    return
  }
  
  try {
    // 清空现有业务数据
    vectorSource.clear()
    
    let businessData: BusinessData[] = []
    
    // 根据不同tab类型加载对应的业务数据
    switch (tabType) {
      case 'residential':
        // TODO: 调用楼栋数据API
        businessData = await loadResidentialData()
        break
      case 'community':
        // TODO: 调用小区数据API
        businessData = await loadCommunityData()
        break
      case 'district':
        // TODO: 调用社区数据API
        businessData = await loadDistrictData()
        break
      case 'block':
        // TODO: 调用街区数据API
        businessData = await loadBlockData()
        break
    }
    
    // 将业务数据添加到地图
    businessData.forEach((item: BusinessData) => {
      if (item.geom) {
        try {
          const geometry = wktFormat.readGeometry(item.geom, {
            dataProjection: 'EPSG:4326',
            featureProjection: 'EPSG:4326'
          })
          
          const feature = new Feature({
            geometry: geometry,
            businessData: item,
            businessId: item.id,
            businessType: tabType
          })
          
          vectorSource.addFeature(feature)
        } catch (error) {
          console.warn(`业务数据 ${item.id} 的地理数据解析失败:`, error)
        }
      }
    })
    
    console.log(`已加载 ${businessData.length} 个${getTaskTypeLabel(tabType)}业务数据到地图`)
  } catch (error) {
    console.error(`加载${getTaskTypeLabel(tabType)}业务数据失败:`, error)
  }
}

// 预留：楼栋数据加载方法
const loadResidentialData = async (): Promise<BusinessData[]> => {
  // TODO: 实现楼栋数据API调用
  console.log('加载楼栋数据...')
  return []
}

// 预留：小区数据加载方法
const loadCommunityData = async (): Promise<BusinessData[]> => {
  // TODO: 实现小区数据API调用
  console.log('加载小区数据...')
  return []
}

// 预留：社区数据加载方法
const loadDistrictData = async (): Promise<BusinessData[]> => {
  // TODO: 实现社区数据API调用
  console.log('加载社区数据...')
  return []
}

// 预留：街区数据加载方法
const loadBlockData = async (): Promise<BusinessData[]> => {
  // TODO: 实现街区数据API调用
  console.log('加载街区数据...')
  return []
}

// 更新tab计数
const updateTabCounts = () => {
  // TODO: 可以调用API获取各个类型的任务数量
  // 这里暂时使用静态数据作为示例
  taskTypeOptions.value.forEach(option => {
    tabCounts.value[option.value] = 0
  })
}

// 防抖查询函数
const debouncedSearch = debounce(() => {
  queryParams.pageNo = 1
  selectedTaskId.value = null // 重置选中状态
  getList()
}, 500) // 500毫秒延迟

// 处理搜索输入
const handleSearchInput = () => {
  debouncedSearch()
}

// 处理搜索清除
const handleSearchClear = () => {
  queryParams.pageNo = 1
  selectedTaskId.value = null // 重置选中状态
  getList()
}
</script>

<style scoped>
.task-layout {
  display: flex;
  height: calc(100vh - 50px);
  gap: 16px;
}

.map-section {
  flex: 0 0 60%;
  background: #f5f5f5;
  border-radius: 8px;
  overflow: hidden;
}

.content-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.search-section {
  flex-shrink: 0;
  margin-bottom: 16px;
}

.tab-section {
  flex-shrink: 0;
  margin-bottom: 16px;
}

.tab-section :deep(.el-card__body) {
  padding: 0;
}

.task-tabs {
  width: 100%;
}

.task-tabs :deep(.el-tabs__header) {
  margin: 0;
  padding: 0 20px;
  background-color: #f8f9fa;
  border-bottom: 1px solid #e4e7ed;
}

.task-tabs :deep(.el-tabs__nav-wrap) {
  padding: 10px 0;
}

.task-tabs :deep(.el-tabs__item) {
  font-weight: 500;
  color: #606266;
  border: none;
  border-radius: 6px 6px 0 0;
  margin-right: 8px;
}

.task-tabs :deep(.el-tabs__item.is-active) {
  background-color: #fff;
  color: #409eff;
  border-bottom: 2px solid #409eff;
}

.tab-label {
  display: flex;
  align-items: center;
  gap: 6px;
}

.tab-badge {
  margin-left: 4px;
}

.tab-badge :deep(.el-badge__content) {
  font-size: 11px;
  height: 16px;
  line-height: 16px;
  min-width: 16px;
  padding: 0 4px;
}

.table-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.table-section :deep(.el-card__body) {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 20px;
}

.table-section :deep(.el-table) {
  flex: 1;
}

.table-section :deep(.pagination-container) {
  flex-shrink: 0;
  margin-top: 16px;
}

.custom-mouse-position {
  position: absolute !important;
  bottom: 8px !important;
  right: 10px !important;
  color: #fff;
  background-color: rgba(0, 0, 0, 0.5);
  padding: 5px 10px;
  border-radius: 4px;
  font-size: 14px;
  z-index: 1000;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

.drawer-footer {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #e4e7ed;
}

/* 选中行样式 */
:deep(.selected-row) {
  background-color: #e8f4ff !important;
}

:deep(.selected-row:hover) {
  background-color: #d1e9ff !important;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .task-layout {
    flex-direction: column;
    height: auto;
  }
  
  .map-section {
    flex: none;
    height: 400px;
  }
  
  .content-section {
    flex: none;
  }
}
</style>
