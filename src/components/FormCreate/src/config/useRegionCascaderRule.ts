import { generateUUID } from '@/utils'
import { localeProps, makeRequiredRule } from '@/components/FormCreate/src/utils'

/**
 * 行政区划级联选择器组件规则
 */
export const useRegionCascaderRule = () => {
  const label = '行政区划'
  const name = 'RegionCascader'
  return {
    // 组件基本信息
    icon: 'icon-cascader',
    label,
    name,
    
    // 组件规则定义
    rule() {
      return {
        type: name,
        field: generateUUID(),
        title: label,
        info: '',
        required: false
      }
    },
    
    // 组件属性配置
    props(_, { t }) {
      return localeProps(t, name + '.props', [
        makeRequiredRule(),
        {
          type: 'input',
          field: 'rootParentCode',
          title: '初始父级节点Code',
          value: '220000',
          info: '设置级联选择器的根节点Code，默认为 220000（顶级节点）'
        },
        {
          type: 'switch',
          field: 'returnMultiLevel',
          title: '返回多级结果',
          value: true,
          info: '是否返回包含省市县等多级信息的对象'
        },
        {
          type: 'switch',
          field: 'returnLastLevel',
          title: '只返回最后一级',
          value: false,
          info: '是否只返回选择路径中的最后一级ID'
        },
        {
          type: 'switch',
          field: 'showMultiLevelResult',
          title: '显示多级结果',
          value: true,
          info: '是否在组件下方显示选择的多级结果标签'
        },
        {
          type: 'switch',
          field: 'clearable',
          title: '可清空',
          value: true
        },
        {
          type: 'switch',
          field: 'filterable',
          title: '可搜索',
          value: true
        },
        {
          type: 'switch',
          field: 'disabled',
          title: '禁用',
          value: false
        },
        {
          type: 'switch',
          field: 'readonly',
          title: '只读',
          value: false
        },
        {
          type: 'select',
          field: 'size',
          title: '尺寸',
          value: 'default',
          options: [
            { label: '大', value: 'large' },
            { label: '默认', value: 'default' },
            { label: '小', value: 'small' }
          ]
        },
        {
          type: 'input',
          field: 'width',
          title: '宽度',
          value: '100%',
          info: '级联选择器的宽度，可以是像素值或百分比'
        },
        {
          type: 'textarea',
          field: 'placeholder',
          title: '占位提示文字',
          value: '请选择行政区划',
          props: { rows: 2 }
        },
        {
          type: 'input',
          field: 'cascaderClass',
          title: '自定义类名',
          value: '',
          info: '为级联选择器添加自定义CSS类名'
        }
      ])
    }
  }
}
