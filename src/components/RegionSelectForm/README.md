# RegionSelectForm 区域选择组件

一个基于 Element Plus 的区域选择组件，左侧使用树形结构展示行政区划层级关系，右侧使用穿梭框进行选择确认。支持多级行政区划选择，可以作为独立组件嵌入到页面中使用。

## 功能特性

- 🌳 **左侧树形结构**：展示行政区划的层级关系，支持复选框选择
- 🔄 **右侧穿梭框**：显示已选择的区域，支持移除操作
- 🎯 **级别限制**：可配置只允许选择特定级别的区域
- 🔍 **搜索过滤**：支持在树形结构中搜索区域
- 💾 **懒加载**：树形结构按需加载子节点，提高性能
- 🎨 **批量选择**：支持选择父节点时自动选择符合条件的子节点
- 📦 **页面嵌入**：可直接在页面中使用，无需弹窗
- 🔗 **双向绑定**：支持 v-model 双向数据绑定

## 级别映射

| regionLevel | 级别名称 | 说明 |
|-------------|----------|------|
| 3 | 省 | 省级行政区 |
| 4 | 市 | 市级行政区 |
| 5 | 县区 | 县/区级行政区 |
| 6 | 镇街 | 镇/街道级行政区 |
| 7 | 社区村 | 社区/村级行政区 |
| 8 | 小区 | 小区级行政区 |

## Props

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| modelValue | RegionVO[] | [] | 当前选中的区域列表，支持 v-model |
| rootParentCode | string \| number | '-99' | 根节点父级编码 |
| selectableLevel | number[] | [3,4,5,6,7,8] | 可选择的级别数组 |
| height | string | '400px' | 组件高度 |
| multiple | boolean | true | 是否支持多选 |
| placeholder | string | '请选择区域' | 占位符文本 |

## Events

| 事件名 | 参数 | 说明 |
|--------|------|------|
| update:modelValue | selectedRegions: RegionVO[] | v-model 更新事件 |
| change | selectedRegions: RegionVO[] | 选择变化时触发 |

## 使用示例

### 基础用法（页面嵌入）

```vue
<template>
  <div>
    <h3>选择区域</h3>
    <div style="height: 400px;">
      <RegionSelectForm
        v-model="selectedRegions"
        @change="handleRegionChange"
      />
    </div>
    <div>
      <p>已选择的区域：</p>
      <ul>
        <li v-for="region in selectedRegions" :key="region.id">
          {{ region.name }} ({{ levelNameMap[region.regionLevel] }})
        </li>
      </ul>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import RegionSelectForm from '@/components/RegionSelectForm/index.vue'

const selectedRegions = ref([])

const handleRegionChange = (regions) => {
  console.log('选中的区域:', regions)
}

const levelNameMap = {
  3: '省', 4: '市', 5: '县区', 6: '镇街', 7: '社区村', 8: '小区'
}
</script>
```

### 只允许选择社区和小区

```vue
<template>
  <div style="height: 400px;">
    <RegionSelectForm
      v-model="selectedRegions"
      :selectable-level="[7, 8]"
      @change="handleRegionChange"
    />
  </div>
</template>

<script setup>
const selectedRegions = ref([])

const handleRegionChange = (regions) => {
  // regions 只包含 regionLevel 为 7 或 8 的区域
  console.log('选中的社区和小区:', regions)
}
</script>
```

### 预设选中项

```vue
<template>
  <RegionSelectForm
    v-model="selectedRegions"
    @change="handleRegionChange"
  />
</template>

<script setup>
const selectedRegions = ref([
  { id: '1', name: '某社区', regionLevel: 7, code: 'xxx' },
  { id: '2', name: '某小区', regionLevel: 8, code: 'yyy' }
])

const handleRegionChange = (regions) => {
  console.log('当前选中:', regions)
}
</script>
```

### 设置不同的根节点

```vue
<template>
  <RegionSelectForm
    v-model="selectedRegions"
    root-parent-code="220000"
    @change="handleRegionChange"
  />
</template>
```

### 在对话框中使用

```vue
<template>
  <Dialog v-model="dialogVisible" title="选择区域" width="900px">
    <div style="height: 500px;">
      <RegionSelectForm
        v-model="selectedRegions"
        :selectable-level="[7, 8]"
        @change="handleRegionChange"
      />
    </div>
    <template #footer>
      <el-button @click="dialogVisible = false">取消</el-button>
      <el-button type="primary" @click="confirmSelection">确定</el-button>
    </template>
  </Dialog>
</template>

<script setup>
const dialogVisible = ref(false)
const selectedRegions = ref([])

const handleRegionChange = (regions) => {
  console.log('选择变化:', regions)
}

const confirmSelection = () => {
  console.log('确认选择:', selectedRegions.value)
  dialogVisible.value = false
}
</script>
```

## 方法

| 方法名 | 参数 | 说明 |
|--------|------|------|
| reload | - | 重新加载数据 |
| clear | - | 清空选择 |

## 注意事项

1. 组件依赖 `RegionApi.getRegionList` 接口，确保接口可用
2. 左侧树形结构使用懒加载，首次点击节点时会请求子节点数据
3. 可选级别配置会影响树节点的复选框显示（不可选择的节点复选框被禁用）
4. 组件支持 v-model 双向绑定，可直接在页面中使用
5. **建议为组件容器设置合适的高度**（如 400px-500px）以获得最佳显示效果
6. 支持批量选择：选择父节点时会自动选择所有符合条件的子节点
7. 左侧树形结构支持搜索功能，可快速定位目标区域
8. 选择状态在左侧树和右侧列表之间实时同步
