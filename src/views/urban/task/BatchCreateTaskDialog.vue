<template>
  <Dialog :title="'批量创建任务'" v-model="dialogVisible" width="1000px" class="batch-create-dialog">
    <div class="dialog-content">
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="120px"
        v-loading="formLoading"
        class="batch-form"
      >
        <!-- 基础配置区域 -->
        <div class="form-section">
          <div class="section-title">
            <Icon icon="ep:setting" class="section-icon" />
            基础配置
          </div>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="城市指标体系" prop="citystandardId">
                <el-select
                  v-model="formData.citystandardId"
                  placeholder="请选择城市指标体系"
                  clearable
                  filterable
                  class="!w-full"
                >
                  <el-option
                    v-for="standard in standardList"
                    :key="standard.id"
                    :label="`${standard.name} (${standard.publishYear}年)`"
                    :value="standard.id"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="目标类型" prop="targetType">
                <el-radio-group v-model="formData.targetType" @change="handleTargetTypeChange" class="target-type-group">
                  <el-radio
                    v-for="option in targetTypeOptions"
                    :key="option.value"
                    :label="option.value"
                    class="target-type-radio"
                  >
                    {{ option.label }}
                  </el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>

          <!-- 手动切分配置 -->
          <el-row v-if="showManualSplit || showSplitCount" :gutter="20">
            <el-col :span="12" v-if="showManualSplit">
              <el-form-item label="手动切分" prop="manualSplit">
                <el-radio-group v-model="formData.manualSplit" @change="handleManualSplitChange">
                  <el-radio :label="true">是</el-radio>
                  <el-radio :label="false">否</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="12" v-if="showSplitCount">
              <el-form-item label="切分份数" prop="splitCount">
                <el-input-number
                  v-model="formData.splitCount"
                  :min="1"
                  :max="100"
                  placeholder="请输入切分份数"
                  class="!w-full"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 区域选择区域 -->
        <div v-if="showRegionSelect" class="form-section">
          <div class="section-title">
            <Icon icon="ep:location" class="section-icon" />
            {{ regionSelectLabel }}
            <span class="section-subtitle">（可选，不选择表示全部）</span>
          </div>
          <div class="region-select-container">
            <RegionSelectForm
              :key="`${formData.targetType}-${regionSelectableLevel.join(',')}`"
              v-model="formData.selectedRegions"
              :root-parent-code="222401"
              :selectable-level="regionSelectableLevel"
              :height="'350px'"
              @change="handleRegionChange"
            />
          </div>
        </div>
      </el-form>
    </div>
    
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">完 成</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { getStrDictOptions, DICT_TYPE } from '@/utils/dict'
import { TaskApi, BatchCreateTaskVO } from '@/api/urban/task'
import { CitystandardApi, CitystandardVO } from '@/api/urban/citystandard'
import { RegionVO } from '@/api/urban/region'
import RegionSelectForm from '@/components/RegionSelectForm/index.vue'

/** 批量创建任务对话框 */
defineOptions({ name: 'BatchCreateTaskDialog' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const formLoading = ref(false) // 表单的加载中
const standardList = ref<CitystandardVO[]>([]) // 城市标准体系列表
const currentTaskType = ref<string>() // 当前任务类型

const formData = ref({
  taskType: undefined as string | undefined,
  citystandardId: undefined as number | undefined,
  targetType: undefined as string | undefined,
  manualSplit: undefined as boolean | undefined,
  splitCount: undefined as number | undefined,
  selectedRegions: [] as RegionVO[] // 选中的区域列表
})

// 获取任务类型字典选项
const taskTypeOptions = computed(() => getStrDictOptions(DICT_TYPE.UC_TASK_TYPE))

// 根据任务类型value获取label
const getTaskTypeLabel = (value: string): string => {
  const option = taskTypeOptions.value.find(opt => opt.value === value)
  return option?.label || value
}

// 根据任务类型动态计算目标类型选项
const targetTypeOptions = computed(() => {
  const currentLabel = getTaskTypeLabel(currentTaskType.value || '')

  switch (currentLabel) {
    case '住房': // 住房
      return [
        { label: '小区', value: '小区' },
        { label: '社区', value: '社区' },
        { label: '街区', value: '街区' }
      ]
    case '小区': // 小区
      return [
        { label: '社区', value: '社区' },
        { label: '街区', value: '街区' }
      ]
    case '社区': // 社区
      return [
        { label: '街区', value: '街区' }
      ]
    case '街区': // 街区
      return [
        { label: '城区', value: '城区' }
      ]
    default:
      return []
  }
})

// 是否显示区域选择
const showRegionSelect = computed(() => {
  return formData.value.targetType && formData.value.targetType !== '城区'
})

// 区域选择标签
const regionSelectLabel = computed(() => {
  if (!formData.value.targetType) return '选择区域'
  return `选择${formData.value.targetType}范围`
})

// 区域选择的可选级别
const regionSelectableLevel = computed(() => {
  switch (formData.value.targetType) {
    case '小区':
      return [8] // 小区级别
    case '社区':
      return [7] // 社区村级别
    case '街道':
    case '街区':
      return [6] // 镇街级别
    default:
      return [6, 7, 8]
  }
})

// 是否显示手动切分选项
const showManualSplit = computed(() => {
  const currentLabel = getTaskTypeLabel(currentTaskType.value || '')
  // 只有当任务类型是"住房"且目标类型是"社区"时才显示
  return (currentLabel === '住房' && formData.value.targetType === '社区') || (currentLabel === '住房' && formData.value.targetType === '街区')
})

// 是否显示切分份数
const showSplitCount = computed(() => {
  return showManualSplit.value && formData.value.manualSplit === true
})

const formRules = reactive({
  citystandardId: [{ required: true, message: '城市指标体系不能为空', trigger: 'change' }],
  targetType: [{ required: true, message: '目标类型不能为空', trigger: 'change' }],
  splitCount: [
    { required: true, message: '切分份数不能为空', trigger: 'blur' },
    { type: 'number', min: 1, max: 100, message: '切分份数必须在1-100之间', trigger: 'blur' }
  ]
})

const formRef = ref() // 表单 Ref

/** 获取城市标准体系列表 */
const getStandardList = async () => {
  try {
    const data = await CitystandardApi.getCitystandardList({ enabled: 1 })
    standardList.value = data
  } catch (error) {
    console.error('获取城市标准体系列表失败:', error)
  }
}

/** 处理目标类型变化 */
const handleTargetTypeChange = (value: string) => {
  console.log('目标类型变化:', value)

  // 清除区域选择
  formData.value.selectedRegions = []

  // 清除手动切分相关字段（会根据 showManualSplit 计算属性重新判断是否显示）
  formData.value.manualSplit = undefined
  formData.value.splitCount = undefined
}

/** 处理区域选择变化 */
const handleRegionChange = (selectedRegions: RegionVO[]) => {
  formData.value.selectedRegions = selectedRegions
  console.log('选中的区域:', selectedRegions)
}

/** 处理手动切分变化 */
const handleManualSplitChange = (value: boolean) => {
  if (!value) {
    // 如果不手动切分，清除切分份数
    formData.value.splitCount = undefined
  }
}

/** 打开弹窗 */
const open = async (taskType?: string) => {
  dialogVisible.value = true
  resetForm()

  // 设置任务类型
  if (taskType) {
    formData.value.taskType = taskType
    currentTaskType.value = taskType

    // 根据任务类型设置默认目标类型
    const options = targetTypeOptions.value
    if (options.length === 1) {
      // 如果只有一个选项，自动选中
      formData.value.targetType = options[0].value
    }
  }

  await getStandardList()
}

defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  if (!formRef.value) return
  const valid = await formRef.value.validate()
  if (!valid) return
  
  // 提交请求
  formLoading.value = true
  try {
    const data: BatchCreateTaskVO = {
      taskType: formData.value.taskType!,
      citystandardId: formData.value.citystandardId!,
      targetType: formData.value.targetType!,
      manualSplit: formData.value.manualSplit,
      splitCount: formData.value.splitCount,
      selectedRegionIds: formData.value.selectedRegions.map(region => region.id),
      initRootRegionCode: '222401'
    }
    
    await TaskApi.batchCreateTask(data)
    message.success('批量创建任务成功')
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } catch (error) {
    console.error('批量创建任务失败:', error)
    message.error('批量创建任务失败')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    taskType: undefined,
    citystandardId: undefined,
    targetType: undefined,
    manualSplit: undefined,
    splitCount: undefined,
    selectedRegions: []
  }
  currentTaskType.value = undefined
  formRef.value?.resetFields()
}
</script>

<style scoped>
.batch-create-dialog {
  .dialog-content {
    padding: 0;
  }

  .batch-form {
    padding: 0;
  }
}

.form-section {
  margin-bottom: 24px;
  padding: 20px;
  background: #fafbfc;
  border-radius: 8px;
  border: 1px solid #e4e7ed;
}

.form-section:last-child {
  margin-bottom: 0;
}

.section-title {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  border-bottom: 1px solid #e4e7ed;
  padding-bottom: 8px;
}

.section-icon {
  margin-right: 8px;
  color: #409eff;
}

.section-subtitle {
  margin-left: 8px;
  font-size: 12px;
  font-weight: normal;
  color: #909399;
}

.target-type-group {
  width: 100%;
}

.target-type-radio {
  margin-right: 16px;
  margin-bottom: 8px;
}

.region-select-container {
  background: #fff;
  border-radius: 6px;
  border: 1px solid #dcdfe6;
  padding: 16px;
}

.el-input-number {
  width: 100%;
}

:deep(.el-form-item) {
  margin-bottom: 18px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: #606266;
}

:deep(.el-radio-group) {
  display: flex;
  flex-wrap: wrap;
}

:deep(.el-radio) {
  margin-right: 16px;
  margin-bottom: 8px;
}

/* 对话框样式优化 */
:deep(.el-dialog) {
  border-radius: 12px;
  overflow: hidden;
}

:deep(.el-dialog__header) {
  background: linear-gradient(135deg, #409eff 0%, #67c23a 100%);
  color: white;
  padding: 20px 24px;
}

:deep(.el-dialog__title) {
  color: white;
  font-weight: 600;
  font-size: 18px;
}

:deep(.el-dialog__body) {
  padding: 24px;
  max-height: 70vh;
  overflow-y: auto;
}

:deep(.el-dialog__footer) {
  padding: 16px 24px;
  background: #f8f9fa;
  border-top: 1px solid #e4e7ed;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .form-section {
    padding: 16px;
  }

  :deep(.el-col) {
    margin-bottom: 16px;
  }
}
</style>
