<template>
  <div class="dynamic-checkbox-with-upload">
    <!-- 复选框 -->
    <el-checkbox
      v-model="checkboxValue"
      :disabled="disabled || readonly"
      :size="size"
      @change="handleCheckboxChange"
    >
      {{ checkboxLabel }}
    </el-checkbox>
    
    <!-- 动态显示的上传组件 -->
    <div v-if="showUpload" class="upload-container">
      <div v-if="uploadLabel" class="upload-label">{{ uploadLabel }}</div>
      <UploadImgs
        :model-value="uploadValue"
        @update:model-value="handleUploadChange"
        :disabled="disabled || readonly"
        :drag="uploadProps.drag"
        :file-type="uploadProps.fileType"
        :file-size="uploadProps.fileSize"
        :limit="uploadProps.limit"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, nextTick } from 'vue'
import { ElCheckbox } from 'element-plus'
import { UploadImgs } from '@/components/UploadFile'

// 定义组件名称
defineOptions({
  name: 'DynamicCheckboxWithUpload'
})

// 组件属性
interface DynamicCheckboxWithUploadProps {
  /** 绑定值 - 包含复选框和上传文件的对象 */
  modelValue?: {
    checked?: boolean
    images?: string[]
  }
  /** 复选框标签文本 */
  checkboxLabel?: string
  /** 上传组件标签文本 */
  uploadLabel?: string
  /** 是否禁用 */
  disabled?: boolean
  /** 是否只读 */
  readonly?: boolean
  /** 尺寸 */
  size?: 'large' | 'default' | 'small'
  /** 上传组件配置 */
  uploadProps?: {
    drag?: boolean
    fileType?: string[]
    fileSize?: number
    limit?: number
  }
}

const props = withDefaults(defineProps<DynamicCheckboxWithUploadProps>(), {
  modelValue: () => ({ checked: false, images: [] }),
  checkboxLabel: '启用上传',
  uploadLabel: '',
  disabled: false,
  readonly: false,
  size: 'default',
  uploadProps: () => ({
    drag: false,
    fileType: ['image/jpeg', 'image/png', 'image/gif'],
    fileSize: 5,
    limit: 5
  })
})

// 组件事件
const emit = defineEmits<{
  'update:modelValue': [value: { checked: boolean; images: string[] }]
  'change': [value: { checked: boolean; images: string[] }]
  'checkbox-change': [checked: boolean]
  'upload-change': [images: string[]]
}>()

// 响应式数据
const checkboxValue = ref(props.modelValue?.checked || false)
const uploadValue = ref<string[]>(Array.isArray(props.modelValue?.images) ? props.modelValue.images : [])

// 计算属性
const showUpload = computed(() => checkboxValue.value)

// 方法
const updateModelValue = () => {
  const newValue = {
    checked: checkboxValue.value,
    images: uploadValue.value
  }
  emit('update:modelValue', newValue)
  emit('change', newValue)
}

const handleCheckboxChange = (checked: boolean) => {
  console.log('🔘 复选框状态变化:', checked)

  // 如果取消勾选，清空上传的图片
  if (!checked) {
    uploadValue.value = []
  }

  updateModelValue()
  emit('checkbox-change', checked)
}

const handleUploadChange = (images: string[]) => {
  console.log('📸 上传文件变化:', images, '类型:', typeof images, '是否数组:', Array.isArray(images))

  // 确保 images 是数组类型
  if (!Array.isArray(images)) {
    console.warn('⚠️ 上传组件返回的不是数组类型:', images)
    return
  }

  // 更新本地值
  uploadValue.value = images
  updateModelValue()
  emit('upload-change', images)
}

// 监听器
watch(() => props.modelValue, (newValue) => {
  // 处理外部值变化，但避免循环更新
  if (newValue) {
    const newChecked = newValue.checked || false
    const newImages = Array.isArray(newValue.images) ? newValue.images : []

    // 只有在值真正不同时才更新
    if (checkboxValue.value !== newChecked) {
      checkboxValue.value = newChecked
    }
    if (JSON.stringify(uploadValue.value) !== JSON.stringify(newImages)) {
      uploadValue.value = newImages
    }
  } else {
    // 如果外部值为空，设置默认值
    checkboxValue.value = false
    uploadValue.value = []
  }
}, { immediate: true, deep: true })

// 组件挂载时初始化
onMounted(() => {
  // 确保组件挂载后立即设置默认值
  nextTick(() => {
    console.log('🚀 DynamicCheckboxWithUpload 组件挂载，初始化默认值')
    updateModelValue()
  })
})

// 暴露方法
defineExpose({
  /** 获取复选框状态 */
  getCheckboxValue: () => checkboxValue.value,
  /** 获取上传文件列表 */
  getUploadValue: () => uploadValue.value,
  /** 获取完整值 */
  getValue: () => ({
    checked: checkboxValue.value,
    images: uploadValue.value
  }),
  /** 设置复选框状态 */
  setCheckboxValue: (checked: boolean) => {
    checkboxValue.value = checked
    if (!checked) {
      uploadValue.value = []
    }
    updateModelValue()
  },
  /** 设置上传文件列表 */
  setUploadValue: (images: string[]) => {
    uploadValue.value = images
    updateModelValue()
  },
  /** 设置完整值 */
  setValue: (value: { checked: boolean; images: string[] }) => {
    checkboxValue.value = value.checked
    uploadValue.value = value.images
    updateModelValue()
  },
  /** 清空 */
  clear: () => {
    checkboxValue.value = false
    uploadValue.value = []
    updateModelValue()
  }
})
</script>

<style lang="scss" scoped>
.dynamic-checkbox-with-upload {
  .upload-container {
    margin-top: 12px;
    padding: 12px;
    border: 1px solid #e4e7ed;
    border-radius: 6px;
    background-color: #fafafa;
    
    .upload-label {
      margin-bottom: 8px;
      font-size: 14px;
      color: #606266;
      font-weight: 500;
    }
  }
}
</style>
