<template>
  <el-cascader
    v-model="currentValue"
    :options="[]"
    :props="cascaderProps"
    :placeholder="placeholder"
    :disabled="disabled"
    :clearable="clearable"
    :filterable="filterable"
    :size="size"
    :style="computedStyle"
    @change="handleChange"
    @expand-change="handleExpandChange"
    @blur="handleBlur"
    @focus="handleFocus"
    @visible-change="handleVisibleChange"
    @remove-tag="handleRemoveTag"
  />
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue'
import { ElCascader } from 'element-plus'
import { RegionApi } from '@/api/urban/region'
import type { RegionCascaderProps, RegionCascaderEmits, RegionNode, RegionLevelMap } from './types'

// 定义组件名称
defineOptions({
  name: 'RegionCascader'
})

// 组件属性
const props = withDefaults(defineProps<RegionCascaderProps>(), {
  modelValue: '',
  rootParentCode: '220000',
  returnMultiLevel: true,
  returnLastLevel: false,
  placeholder: '请选择行政区划',
  disabled: false,
  readonly: false,
  clearable: true,
  filterable: true,
  size: 'default',
  width: '100%'
})

// 组件事件
const emit = defineEmits<RegionCascaderEmits>()

// 响应式数据
const currentValue = ref<string[]>([])
const treeLoadedCache = ref(new Map<string, RegionNode[]>())
const parentPathCache = ref(new Map<string, any>())

// 计算属性
const computedStyle = computed(() => {
  const style: any = {}
  if (props.width) {
    style.width = typeof props.width === 'number' ? `${props.width}px` : props.width
  }
  if (typeof props.style === 'string') {
    return `${style}; ${props.style}`
  }
  return { ...style, ...props.style }
})

// 统一的叶子节点判断函数
const isLeafNode = (regionLevel: number): boolean => {
  // 层级 >= 7 的为叶子节点（社区村及以下）
  return regionLevel >= 8
}

// 级联选择器属性配置
const cascaderProps = {
  label: 'label',
  value: 'value',
  children: 'children',
  leaf: 'leaf',
  lazy: true,
  checkStrictly: true,
  lazyLoad: (node: any, resolve: (data: RegionNode[]) => void) => loadRegionTree(node, resolve)
}

// 懒加载节点
const loadRegionTree = async (node: any, resolve: (data: RegionNode[]) => void) => {
  try {
    // 确定要加载的 parentCode
    let parentCode = props.rootParentCode
    if (node && node.level > 0) {
      parentCode = node.value
    }

    // 检查缓存中是否已有数据
    if (treeLoadedCache.value.has(parentCode)) {
      const cachedData = treeLoadedCache.value.get(parentCode)!
      resolve(cachedData)
      return
    }

    // 从API加载数据
    const data = await RegionApi.getRegionList({ parentCode })
    const nodes: RegionNode[] = data.map(item => ({
      value: item.code,  // 使用 code 作为 value
      label: item.name,
      leaf: isLeafNode(item.regionLevel),
      ...item // 保留原始数据
    }))

    // 更新缓存 - 同时缓存到 parentPathCache 以便生成多级结果
    treeLoadedCache.value.set(parentCode, nodes)
    nodes.forEach(node => {
      parentPathCache.value.set(node.code, node)  // 使用 code 作为 key
    })

    resolve(nodes)
  } catch (error) {
    console.error('加载区域节点失败', error)
    resolve([])
  }
}

// 加载父节点路径，用于回显
const loadParentPath = async (code: string) => {
  try {
    // 递归获取完整的父节点路径
    const path: string[] = []
    let currentCode = code

    while (currentCode && currentCode !== props.rootParentCode) {
      const regionInfo = await RegionApi.getRegionByCode(currentCode)
      if (regionInfo) {
        path.unshift(regionInfo.code)  // 使用 code
        parentPathCache.value.set(regionInfo.code, regionInfo)  // 使用 code 作为 key
        // 需要获取父级的 code，这里假设有 parentCode 字段，如果没有需要通过 parentId 查询
        currentCode = regionInfo.parentCode || (regionInfo.parentId ? (await RegionApi.getRegion(regionInfo.parentId))?.code : null)
      } else {
        break
      }
    }

    // 预加载路径上的节点数据
    for (let i = 0; i <= path.length; i++) {
      let parentCode: string
      if (i === 0) {
        parentCode = props.rootParentCode
      } else if (i === path.length) {
        parentCode = path[i - 1]
      } else {
        parentCode = path[i - 1]
      }

      if (!treeLoadedCache.value.has(parentCode)) {
        const children = await RegionApi.getRegionList({ parentCode })
        const formattedChildren: RegionNode[] = children.map(item => ({
          value: item.code,  // 使用 code 作为 value
          label: item.name,
          leaf: isLeafNode(item.regionLevel),
          ...item
        }))
        treeLoadedCache.value.set(parentCode, formattedChildren)
        // 同时缓存到 parentPathCache
        formattedChildren.forEach(child => {
          parentPathCache.value.set(child.code, child)
        })
      }
    }

    // 预加载完成后，设置值
    if (path.length > 0) {
      await nextTick()
      currentValue.value = path
    }
  } catch (error) {
    console.error('加载父节点路径失败', error)
  }
}

// 根据区域级别生成多级返回结果
const generateMultiLevelResult = async (selectedPath: string[]): Promise<RegionLevelMap> => {
  const result: RegionLevelMap = {}

  for (const nodeCode of selectedPath) {
    let nodeInfo = parentPathCache.value.get(nodeCode)

    // 如果缓存中没有，从API获取
    if (!nodeInfo) {
      try {
        nodeInfo = await RegionApi.getRegionByCode(nodeCode)
        if (nodeInfo) {
          parentPathCache.value.set(nodeCode, nodeInfo)
        }
      } catch (error) {
        console.warn(`获取区域信息失败: ${nodeCode}`, error)
        continue
      }
    }

    if (nodeInfo) {
      const level = nodeInfo.regionLevel
      switch (level) {
        case 3:
          result.province = nodeInfo.code  // 使用 code
          break
        case 4:
          result.city = nodeInfo.code  // 使用 code
          break
        case 5:
          result.xzqdm = nodeInfo.code  // 使用 code
          break
        case 6:
          result.town = nodeInfo.code  // 使用 code
          break
        case 7:
          result.village = nodeInfo.code  // 使用 code
          break
        case 8:
          result.community = nodeInfo.code  // 使用 code
          break
      }
    }
  }

  return result
}

// 事件处理
const handleChange = async (value: string[]) => {
  const finalValue = props.returnLastLevel && value.length > 0 ? value[value.length - 1] : value

  emit('update:modelValue', finalValue)

  if (props.returnMultiLevel && value.length > 0) {
    try {
      const multiLevelResult = await generateMultiLevelResult(value)
      console.log('🎯 生成多级结果:', multiLevelResult)
      emit('change', finalValue, multiLevelResult)
    } catch (error) {
      console.error('生成多级结果失败:', error)
      emit('change', finalValue, null)
    }
  } else {
    emit('change', finalValue, null)
  }
}

const handleExpandChange = (value: string[]) => {
  emit('expand-change', value)
}

const handleBlur = (event: FocusEvent) => {
  emit('blur', event)
}

const handleFocus = (event: FocusEvent) => {
  emit('focus', event)
}

const handleVisibleChange = (visible: boolean) => {
  emit('visible-change', visible)
}

const handleRemoveTag = (value: string) => {
  emit('remove-tag', value)
}

// 监听器
watch(() => props.modelValue, async (newValue) => {
  if (!newValue) {
    currentValue.value = []
    return
  }

  // 如果是字符串，需要加载父节点路径（现在使用 code）
  if (typeof newValue === 'string') {
    await loadParentPath(newValue)
  } else if (Array.isArray(newValue)) {
    currentValue.value = newValue
  }
}, { immediate: true })

// 暴露方法
defineExpose({
  /** 清空选择 */
  clear: () => {
    currentValue.value = []
    emit('update:modelValue', props.returnLastLevel ? '' : [])
    emit('change', props.returnLastLevel ? '' : [], null)
  },
  /** 获取当前值 */
  getValue: () => currentValue.value,
  /** 设置值 */
  setValue: (value: string | string[]) => {
    if (typeof value === 'string') {
      loadParentPath(value)
    } else {
      currentValue.value = value
    }
  }
})
</script>
