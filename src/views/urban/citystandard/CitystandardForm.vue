<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible" width="600px">
    <div class="form-container">
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="100px"
        v-loading="formLoading"
        class="citystandard-form"
      >
        <!-- 基本信息分组 -->
        <div class="form-section">
          <div class="section-title">
            <Icon icon="ep:document" class="section-icon" />
            <span>基本信息</span>
          </div>

          <el-form-item label="名称" prop="name">
            <el-input
              v-model="formData.name"
              placeholder="请输入标准体系名称"
              :prefix-icon="'ep:edit'"
              clearable
            />
          </el-form-item>

          <el-form-item label="发布年份" prop="publishYear">
            <el-date-picker
              v-model="formData.publishYear"
              type="year"
              value-format="YYYY"
              placeholder="选择发布年份"
              :prefix-icon="'ep:calendar'"
              style="width: 100%"
              clearable
            />
          </el-form-item>
        </div>

        <el-divider />

        <!-- 配置信息分组 -->
        <div class="form-section">
          <div class="section-title">
            <Icon icon="ep:setting" class="section-icon" />
            <span>配置信息</span>
          </div>

          <el-form-item label="分类" prop="category">
            <el-select
              v-model="formData.category"
              placeholder="请选择分类"
              clearable
              style="width: 100%"
            >
              <el-option
                v-for="dict in getStrDictOptions(DICT_TYPE.UC_CITYSTANDARD_CATEGORY)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="是否启用" prop="enabled">
            <el-radio-group v-model="formData.enabled" class="radio-group">
              <el-radio
                v-for="dict in getIntDictOptions(DICT_TYPE.UC_CITYSTANDARD_ENABLED)"
                :key="dict.value"
                :value="dict.value"
                class="radio-item"
              >
                {{ dict.label }}
              </el-radio>
            </el-radio-group>
          </el-form-item>
        </div>
      </el-form>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false" size="large">
          <Icon icon="ep:close" class="mr-5px" />
          取 消
        </el-button>
        <el-button
          @click="submitForm"
          type="primary"
          :disabled="formLoading"
          :loading="formLoading"
          size="large"
        >
          <Icon icon="ep:check" class="mr-5px" />
          确 定
        </el-button>
      </div>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { getIntDictOptions, getStrDictOptions, DICT_TYPE } from '@/utils/dict'
import { CitystandardApi, CitystandardVO } from '@/api/urban/citystandard'

/** 城市标准体体系 表单 */
defineOptions({ name: 'CitystandardForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  name: undefined,
  publishYear: 0,
  category: undefined,
  enabled: undefined
})
const formRules = reactive({
  name: [{ required: true, message: '名称不能为空', trigger: 'blur' }],
  publishYear: [{ required: true, message: '发布年份不能为空', trigger: 'blur' }],
  category: [{ required: true, message: '分类不能为空', trigger: 'blur' }],
  enabled: [{ required: true, message: '是否启用不能为空', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await CitystandardApi.getCitystandard(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as CitystandardVO
    if (formType.value === 'create') {
      await CitystandardApi.createCitystandard(data)
      message.success(t('common.createSuccess'))
    } else {
      await CitystandardApi.updateCitystandard(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    name: undefined,
    publishYear: 0,
    category: undefined,
    enabled: undefined
  }
  formRef.value?.resetFields()
}
</script>

<style lang="scss" scoped>
.form-container {
  padding: 0;
}

.citystandard-form {
  .form-section {
    margin-bottom: 24px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .section-title {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    font-size: 16px;
    font-weight: 600;
    color: #303133;

    .section-icon {
      font-size: 18px;
      color: var(--el-color-primary);
      margin-right: 8px;
    }
  }

  :deep(.el-divider) {
    margin: 24px 0;

    .el-divider__text {
      background-color: #fff;
      color: #909399;
      font-size: 14px;
    }
  }

  :deep(.el-form-item) {
    margin-bottom: 24px;

    &:last-child {
      margin-bottom: 0;
    }

    .el-form-item__label {
      font-weight: 500;
      color: #606266;
      line-height: 32px;
    }

    .el-input {
      .el-input__wrapper {
        border-radius: 6px;
        transition: all 0.3s ease;
        box-shadow: 0 0 0 1px #dcdfe6;

        &:hover {
          box-shadow: 0 0 0 1px var(--el-color-primary-light-7);
        }

        &.is-focus {
          box-shadow: 0 0 0 1px var(--el-color-primary);
        }
      }
    }

    .el-date-editor {
      .el-input__wrapper {
        border-radius: 6px;
        transition: all 0.3s ease;
        box-shadow: 0 0 0 1px #dcdfe6;

        &:hover {
          box-shadow: 0 0 0 1px var(--el-color-primary-light-7);
        }

        &.is-focus {
          box-shadow: 0 0 0 1px var(--el-color-primary);
        }
      }
    }

    .el-select {
      .el-input__wrapper {
        border-radius: 6px;
        transition: all 0.3s ease;
        box-shadow: 0 0 0 1px #dcdfe6;

        &:hover {
          box-shadow: 0 0 0 1px var(--el-color-primary-light-7);
        }

        &.is-focus {
          box-shadow: 0 0 0 1px var(--el-color-primary);
        }
      }
    }
  }

  .radio-group {
    display: flex;
    gap: 16px;

    .radio-item {
      margin-right: 0;

      :deep(.el-radio__label) {
        font-weight: 500;
      }

      :deep(.el-radio__input.is-checked .el-radio__inner) {
        background-color: var(--el-color-primary);
        border-color: var(--el-color-primary);
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 16px 0 0 0;

  .el-button {
    border-radius: 6px;
    font-weight: 500;
    min-width: 100px;

    &.el-button--primary {
      background: linear-gradient(135deg, var(--el-color-primary) 0%, var(--el-color-primary-dark-2) 100%);
      border: none;

      &:hover {
        background: linear-gradient(135deg, var(--el-color-primary-light-3) 0%, var(--el-color-primary) 100%);
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .form-container {
    .citystandard-form {
      .form-section {
        margin-bottom: 16px;
      }

      .section-title {
        font-size: 14px;
        margin-bottom: 16px;

        .section-icon {
          font-size: 16px;
        }
      }

      :deep(.el-form-item) {
        margin-bottom: 20px;

        .el-form-item__label {
          font-size: 14px;
        }
      }

      :deep(.el-divider) {
        margin: 20px 0;
      }
    }
  }

  .dialog-footer {
    .el-button {
      min-width: 80px;
    }
  }
}
</style>
