<template>
  <el-dialog
    :title="dialogTitle"
    v-model="dialogVisible"
    width="600px"
    append-to-body
    destroy-on-close
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="80px"
    >
      <el-form-item label="上级区划" prop="parentId">
        <el-cascader
          v-model="formData.parentId"
          :props="cascaderProps"
          placeholder="请选择上级区划"
          class="w-full"
          clearable
        />
      </el-form-item>
      <el-form-item label="排序" prop="sortNo">
        <el-input-number v-model="formData.sortNo" :min="0" class="w-full" />
      </el-form-item>
      <el-form-item label="区划代码" prop="code">
        <el-input v-model="formData.code" placeholder="请输入地区编号" />
      </el-form-item>
      <el-form-item label="区划名称" prop="name">
        <el-input v-model="formData.name" placeholder="请输入地区名称" />
      </el-form-item>
      <el-form-item label="空间范围" prop="geom">
        <div class="map-draw-trigger" @click="openMapDrawDialog">
          <div class="map-draw-status">
            <div class="status-content">
              <Icon
                :icon="hasGeomData ? 'ep:success-filled' : 'ep:warning-filled'"
                :class="hasGeomData ? 'text-green-500' : 'text-orange-500'"
                class="text-lg mr-2"
              />
              <div class="status-text">
                <div class="status-title">
                  {{ hasGeomData ? '已绘制空间范围' : '未绘制空间范围' }}
                </div>
                <div class="status-desc">
                  {{ hasGeomData ? '点击查看或重新绘制区域范围' : '点击在地图上绘制区域空间范围' }}
                </div>
              </div>
            </div>
            <div class="action-buttons">
              <el-button type="primary" size="small">
                <Icon icon="ep:edit" class="mr-1" />
                {{ hasGeomData ? '重新绘制' : '开始绘制' }}
              </el-button>
              <el-button
                v-if="hasGeomData"
                type="danger"
                size="small"
                @click.stop="clearGeomData"
              >
                <Icon icon="ep:delete" class="mr-1" />
                清除
              </el-button>
            </div>
          </div>
        </div>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="dialogVisible = false">取 消</el-button>
      <el-button type="primary" @click="submitForm" :loading="formLoading">确 定</el-button>
    </template>
  </el-dialog>

  <!-- 地图绘制弹窗 -->
  <MapDrawDialog
    v-model:visible="mapDrawDialogVisible"
    v-model="currentGeomData"
    title="绘制区域空间范围"
    :width="'85%'"
    :height="'75%'"
    :map-height="'500px'"
    :show-toolbar="true"
    :allowed-modes="[DrawMode.MULTIPOLYGON]"
    :allow-multiple="false"
    :center="mapCenter"
    :zoom="13"
    :projection="'EPSG:4326'"
    :tianditu-token="'4989e906aa138e5bb1b49a3eb83a6128'"
    confirm-button-text="确认绘制"
    cancel-button-text="取消"
    @confirm="handleMapDrawConfirm"
    @cancel="handleMapDrawCancel"
  />
</template>

<script setup lang="ts">
import { RegionApi } from '@/api/urban/region'
import { ref, reactive, watch, computed } from 'vue'
import MapDrawDialog from '@/components/MapDraw/src/MapDrawDialog.vue'
import { DrawMode } from '@/components/MapDraw/src/types'

const emit = defineEmits(['success'])
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  parentId: '-99',
  name: '',
  code: '',
  fullName: '',
  status: 1,
  sortNo: 0,
  rankName: '',
  regionLevel: 1,
  geom: '' as string | undefined
})
const formRef = ref() // 表单 Ref

/** 表单校验 */
const formRules = reactive({
  name: [{ required: true, message: '区划名称不能为空', trigger: 'blur' }],
  code: [{ required: true, message: '区划代码不能为空', trigger: 'blur' }],
  parentId: [{ required: true, message: '上级区划不能为空', trigger: 'blur' }]
})

/** 级联选择器数据 */
const regionTree = ref([])
// 已加载的节点缓存，防止重复加载或循环依赖
const treeLoadedCache = ref(new Map())
// 父节点路径缓存，用于编辑时展开到选中节点
const parentPathCache = ref(new Map())

// 地图绘制相关
const mapDrawDialogVisible = ref(false) // 地图绘制弹窗显示状态
const currentGeomData = ref('') // 当前地图数据
const mapCenter = ref<[number, number]>([116.404, 39.915]) // 地图中心点（北京）

// 计算属性
const hasGeomData = computed(() => {
  return formData.value.geom && formData.value.geom.length > 0
})

/** 级联选择器属性配置 */
const cascaderProps = {
  label: 'name',
  value: 'id',
  children: 'children',
  leaf: 'leaf',
  lazy: true,
  checkStrictly: true,
  lazyLoad: (node, resolve) => loadRegionTree(node, resolve)
}

/** 打开弹窗 */
const open = async (type, id) => {
  dialogVisible.value = true
  dialogTitle.value = type === 'create' ? '新增行政区划' : '修改行政区划'
  formType.value = type
  resetForm()

  // 清空缓存
  treeLoadedCache.value.clear()
  parentPathCache.value.clear()

  // 清空区域树数据，让 cascader 通过懒加载获取
  regionTree.value = []

  // 修改时，设置数据
  if (type === 'update' && id) {
    formLoading.value = true
    try {
      const regionData = await RegionApi.getRegion(id)
      formData.value = regionData

      // 如果有父节点且不是根节点，获取父节点路径并展开
      if (regionData.parentId  && regionData.parentId !== '-99') {
        await loadParentPath(regionData.parentId)
      }
    } finally {
      formLoading.value = false
    }
  }
}

/** 加载父节点路径，用于 cascader 回显 */
const loadParentPath = async (id) => {
  try {
    // 递归获取完整的父节点路径
    const path = []
    let currentId = id

    while (currentId && currentId !== '-99') {
      const regionInfo = await RegionApi.getRegion(currentId)
      if (regionInfo) {
        path.unshift(regionInfo.id) // 添加到路径开头
        parentPathCache.value.set(regionInfo.id, regionInfo)
        currentId = regionInfo.parentId
      } else {
        break
      }
    }

    // 设置 cascader 的值为完整路径数组
    if (path.length > 0) {
      formData.value.parentId = path
    }

    // 预加载路径上的节点数据
    for (let i = 0; i < path.length - 1; i++) {
      const parentId = i === 0 ? '-99' : path[i - 1]
      if (!treeLoadedCache.value.has(parentId)) {
        const children = await RegionApi.getRegionList({ parentId })
        treeLoadedCache.value.set(parentId, children.map(item => ({
          ...item,
          leaf: item.regionLevel >= 8
        })))
      }
    }
  } catch (error) {
    console.error('加载父节点路径失败', error)
  }
}

/** 懒加载节点 - 按照官方文档格式 */
const loadRegionTree = async (node, resolve) => {
  try {
    console.log('🌍 loadRegionTree 被调用', { node, level: node?.level })

    // 确定要加载的 parentId
    let parentId = '-99' // 根节点
    if (node && node.level > 0) {
      parentId = node.value
      console.log('🌍 加载子节点，parentId:', parentId)
    } else {
      console.log('🌍 加载根节点，parentId:', parentId)
    }

    // 检查缓存中是否已有数据
    if (treeLoadedCache.value.has(parentId)) {
      const cachedData = treeLoadedCache.value.get(parentId)
      console.log('🌍 返回缓存数据', cachedData)
      resolve(cachedData)
      return
    }

    // 从API加载数据
    console.log('🌍 从API加载数据，parentId:', parentId)
    const data = await RegionApi.getRegionList({ parentId })
    const nodes = data.map(item => ({
      value: item.id,
      label: item.name,
      leaf: item.regionLevel >= 7, // 层级大于等于7的为叶子节点
      ...item // 保留原始数据
    }))

    console.log('🌍 成功加载数据', nodes)

    // 更新缓存
    treeLoadedCache.value.set(parentId, nodes)
    resolve(nodes)
  } catch (error) {
    console.error('💥 加载节点失败', error)
    resolve([])
  }
}



/** 重置表单 */
const resetForm = () => {
  formData.value = {
    parentId: '',
    name: '',
    code: '',
    fullName: '',
    status: 1,
    sortNo: 0,
    rankName: '',
    regionLevel: 1,
    geom: ''
  }
  currentGeomData.value = ''
  formRef.value?.resetFields()
}

/** 提交表单 */
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()

  // 处理 cascader 返回的数组值，取最后一个作为实际的 parentId
  let actualParentId = formData.value.parentId
  if (Array.isArray(formData.value.parentId)) {
    actualParentId = formData.value.parentId[formData.value.parentId.length - 1] || '-99'
  }

  // 设置区域层级
  if (actualParentId === '0' || actualParentId === '-99') {
    formData.value.regionLevel = 1
  } else {
    // 先从缓存中查找
    const cachedParent = parentPathCache.value.get(actualParentId)
    if (cachedParent) {
      formData.value.regionLevel = (cachedParent.regionLevel || 0) + 1
    } else {
      // 如果缓存中没有，从API获取
      try {
        const parentInfo = await RegionApi.getRegion(actualParentId)
        formData.value.regionLevel = (parentInfo.regionLevel || 0) + 1
      } catch (error) {
        console.error('获取父节点层级失败', error)
        formData.value.regionLevel = 1 // 默认设为1
      }
    }
  }

  // 设置实际的 parentId 用于提交
  const submitData = {
    ...formData.value,
    parentId: actualParentId
  }

  // 提交请求
  formLoading.value = true
  try {
    if (formType.value === 'create') {
      await RegionApi.createRegion(submitData)
      message.success('新增成功')
    } else {
      await RegionApi.updateRegion(submitData)
      message.success('修改成功')
    }
    // 关闭弹窗，通知父组件刷新
    dialogVisible.value = false
    emit('success', formType.value, submitData)
  } catch {
    // 失败的提示由 request 内部的 showError 实现
  } finally {
    formLoading.value = false
  }
}

// 监听父级区域变化，自动填充地区全名
watch(() => formData.value.parentId, async (newVal) => {
  // 处理 cascader 返回的数组值
  let actualParentId = newVal
  if (Array.isArray(newVal)) {
    actualParentId = newVal[newVal.length - 1]
  }

  if (actualParentId && actualParentId !== '0' && actualParentId !== '-99') {
    try {
      // 如果父级区域变化且有值，可以考虑自动填充地区全名
      const parentInfo = parentPathCache.value.get(actualParentId) || await RegionApi.getRegion(actualParentId)
      if (formData.value.name && parentInfo.fullName) {
        formData.value.fullName = `${parentInfo.fullName}${formData.value.name}`
      }
    } catch (error) {
      console.error('获取父级区域信息失败', error)
    }
  }
})

// 监听地区名称变化，自动更新地区全名
watch(() => formData.value.name, (newVal) => {
  // 处理 cascader 返回的数组值
  let actualParentId = formData.value.parentId
  if (Array.isArray(formData.value.parentId)) {
    actualParentId = formData.value.parentId[formData.value.parentId.length - 1]
  }

  if (newVal && actualParentId && actualParentId !== '0' && actualParentId !== '-99') {
    const parentInfo = parentPathCache.value.get(actualParentId)
    if (parentInfo && parentInfo.fullName) {
      formData.value.fullName = `${parentInfo.fullName}${newVal}`
    }
  } else if (newVal) {
    formData.value.fullName = newVal
  }
})

/** 打开地图绘制弹窗 */
const openMapDrawDialog = () => {
  currentGeomData.value = formData.value.geom || ''
  mapDrawDialogVisible.value = true
}

/** 处理地图绘制确认 */
const handleMapDrawConfirm = (wkt: string) => {
  formData.value.geom = wkt
  mapDrawDialogVisible.value = false
  message.success('区域空间范围绘制完成')
}

/** 处理地图绘制取消 */
const handleMapDrawCancel = () => {
  mapDrawDialogVisible.value = false
}

/** 清除地图数据 */
const clearGeomData = () => {
  formData.value.geom = ''
  currentGeomData.value = ''
  message.info('已清除区域空间范围数据')
}

// 暴露方法
defineExpose({ open })
</script>

<style scoped>
.map-draw-trigger {
  border: 1px solid #dcdfe6;
  border-radius: 6px;
  padding: 16px;
  background-color: #fafafa;
  cursor: pointer;
  transition: all 0.3s ease;
}

.map-draw-trigger:hover {
  border-color: #409eff;
  background-color: #f0f8ff;
}

.map-draw-status {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.status-content {
  display: flex;
  align-items: center;
  flex: 1;
}

.status-text {
  flex: 1;
}

.status-title {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.status-desc {
  font-size: 12px;
  color: #909399;
}

.action-buttons {
  display: flex;
  gap: 8px;
}

.action-buttons .el-button {
  margin: 0;
}
</style>
