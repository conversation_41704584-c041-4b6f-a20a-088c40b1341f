import request from '@/config/axios'

// 任务管理 VO
export interface TaskVO {
  id: number // 主键
  taskId: string // 任务编号
  taskType: string // 任务类型
  taskName: string // 任务名称
  citystandardId?: number // 城市指标体系ID
  province: string // 省
  city: string // 市
  xzqdm: string // 县区
  town: string // 镇街
  village: string // 社区村
  community: string // 小区
  leaderId: string // 组长ID
  leaderName: string // 组长
  surveyorIds: number[] // 调查人员ID数组
  status: number // 状态：0-未完成|1-已完成
  plannedTime: Date // 计划完成时间
  actualDate: Date // 实际完成时间
  geom: object // 图形
}

// 批量创建任务 VO
export interface BatchCreateTaskVO {
  taskType: string // 任务类型
  citystandardId: number // 城市指标体系ID
  targetType: string // 目标类型：小区、社区、街道、城区
  manualSplit?: boolean // 手动切分（仅当targetType为社区时有效）
  splitCount?: number // 切分份数（仅当manualSplit为true时有效）
  selectedRegionIds?: string[] // 选中的区域ID列表
  initRootRegionCode?: string // 初始根区划代码
}

// 任务管理 API
export const TaskApi = {
  // 查询任务管理分页
  getTaskPage: async (params: any) => {
    return await request.get({ url: `/urban/task/page`, params })
  },

  // 查询任务管理详情
  getTask: async (id: number) => {
    return await request.get({ url: `/urban/task/get?id=` + id })
  },

  // 新增任务管理
  createTask: async (data: TaskVO) => {
    return await request.post({ url: `/urban/task/create`, data })
  },

  // 修改任务管理
  updateTask: async (data: TaskVO) => {
    return await request.put({ url: `/urban/task/update`, data })
  },

  // 删除任务管理
  deleteTask: async (id: number) => {
    return await request.delete({ url: `/urban/task/delete?id=` + id })
  },

  // 导出任务管理 Excel
  exportTask: async (params) => {
    return await request.download({ url: `/urban/task/export-excel`, params })
  },

  // 批量创建任务
  batchCreateTask: async (data: BatchCreateTaskVO) => {
    return await request.post({ url: `/urban/task/batchcreate`, data })
  }
}